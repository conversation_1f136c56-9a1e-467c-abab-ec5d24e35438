{"provider": "snowflake", "schemas": [{"schema_id": "3f034b58-01ea-4927-acf8-6893b94ded00", "database_name": "ds_oil_and_gas_dev", "description": "Snowflake schema containing 12 table(s)", "table_count": 12, "tables_overview": [{"table_name": "ds_oil_and_gas_dev.merged_file", "description": "This table contains the following fields: id, project_id, assistant_file_id, status. The 'merged_file' table is designed to store information about files that have been merged as part of a project. It tracks the relationship between projects and their associated files, as well as the current status of each merged file."}, {"table_name": "ds_oil_and_gas_dev.prompt", "description": "This table contains the following fields: id, name, value, type, date_created, date_updated. The 'prompt' table is designed to store various prompts used in an application, capturing their attributes and metadata for easy retrieval and management."}, {"table_name": "ds_oil_and_gas_dev.file_merged_file_association", "description": "This table contains the following fields: id, file_id, merged_file_id, association_type, status. The purpose of this table is to establish and manage the relationships between original files and their corresponding merged files, allowing for tracking of how files are combined and their current status within the system."}, {"table_name": "ds_oil_and_gas_dev.file", "description": "This table contains the following fields: id, name, project_id, file_type, status, merge_status, tried, category, file_dirtry, created_at, updated_at. The 'file' table is designed to store information about files associated with various projects, including their types, statuses, and metadata related to their management and processing."}, {"table_name": "ds_oil_and_gas_dev.disciplines", "description": "This table contains the following fields: id, shortcode, name, created_at, updated_at. The 'disciplines' table is designed to store information about various academic or professional disciplines, allowing for the categorization and management of different fields of study or practice."}, {"table_name": "ds_oil_and_gas_dev.categories", "description": "This table contains the following fields: id, name, feed, detail, vendor, discipline_id, criteria, similar, created_at, updated_at. The 'categories' table is designed to store information about various categories used in the application, allowing for organization and classification of related items or entities. Each category can be linked to a specific discipline and may include additional metadata such as vendor information and criteria for categorization."}, {"table_name": "ds_oil_and_gas_dev.project", "description": "This table contains the following fields: id, name, entity_type, assistant_id, has_deleted_file, discipline_code, created_at, updated_at. The 'project' table is designed to store information about various projects within an organization, including their identifiers, types, associated assistants, and timestamps for creation and updates."}, {"table_name": "ds_oil_and_gas_dev.chunks", "description": "This table contains the following fields: id, title, content, source, summary, project_id, file_id, page_number, created_at, updated_at. The 'chunks' table is designed to store segments of content that are associated with specific projects and files, allowing for organized retrieval and management of textual data."}, {"table_name": "ds_oil_and_gas_dev.alembic_version", "description": "This table is used by Alembic to keep track of the database schema versions. It allows for version control of database migrations, ensuring that the database schema is consistent with the application's codebase. This table contains the following fields: version_num."}, {"table_name": "ds_oil_and_gas_dev.tender", "description": "This table contains the following fields: id, name, project_id, file_type, status, tried, score, criteria. The 'tender' table is used to store information related to tenders submitted for various projects. It tracks the details of each tender, including its identification, associated project, file type, current status, evaluation metrics, and criteria used for assessment."}, {"table_name": "ds_oil_and_gas_dev.tags", "description": "This table contains the following fields: id, name, chunk_id. The 'tags' table is used to store metadata tags that can be associated with various chunks of data in the database, allowing for better organization and retrieval of related information."}, {"table_name": "ds_oil_and_gas_dev.requirement", "description": "This table contains the following fields: id, name, project_id, file_type, category, status, tried, score, criteria, discipline, created_at, updated_at, type, is_test, is_report_generated. The 'requirement' table is designed to store detailed information about various requirements associated with projects, including their attributes, status, and related metadata."}]}, {"schema_id": "1307058d-d983-4bd3-9952-484dcd44d93d", "database_name": "analytics", "description": "Snowflake schema containing 14 table(s)", "table_count": 14, "tables_overview": [{"table_name": "analytics.conversion_funnel", "description": "This table contains the following fields: funnel_id, user_id, stage, stage_date, conversion_value. The 'conversion_funnel' table is designed to track the progress of users through various stages of a conversion process, capturing key metrics that help analyze user behavior and conversion rates."}, {"table_name": "analytics.inventory_forecast", "description": "This table contains the following fields: forecast_id, product_id, forecast_date, predicted_stock, confidence_interval. The 'inventory_forecast' table is used to store predictions regarding inventory levels for various products over time. It helps businesses manage stock levels by forecasting future inventory needs based on historical data and trends."}, {"table_name": "analytics.marketing_campaigns", "description": "This table contains the following fields: campaign_id, campaign_name, start_date, end_date, budget, status. The 'marketing_campaigns' table is used to store information about various marketing campaigns, including their identifiers, names, timelines, financial allocations, and current statuses, enabling effective tracking and management of marketing efforts."}, {"table_name": "analytics.performance_metrics", "description": "This table contains the following fields: metric_id, metric_name, metric_value, metric_date, category. The purpose of the 'performance_metrics' table is to store and track various performance metrics over time, allowing for analysis and reporting on performance trends across different categories."}, {"table_name": "analytics.customer_segments", "description": "This table contains the following fields: segment_id, segment_name, criteria, created_at. The 'customer_segments' table is designed to categorize customers into distinct segments based on specific criteria, allowing businesses to tailor their marketing strategies and improve customer engagement."}, {"table_name": "analytics.customer_lifetime_value", "description": "This table contains the following fields: clv_id, customer_id, calculated_date, predicted_value, confidence_interval. The purpose of this table is to store and manage data related to the predicted lifetime value of customers, which helps businesses understand the long-term profitability of their customer relationships and make informed marketing and sales decisions."}, {"table_name": "analytics.campaign_metrics", "description": "This table contains the following fields: metric_id, campaign_id, metric_name, metric_value, metric_date. The 'campaign_metrics' table is designed to store performance metrics related to various marketing campaigns. It allows for tracking and analyzing the effectiveness of campaigns over time by recording specific metrics associated with each campaign."}, {"table_name": "analytics.churn_prediction", "description": "This table contains the following fields: prediction_id, customer_id, prediction_date, churn_probability, risk_level. The purpose of this table is to store and analyze data related to customer churn predictions, helping businesses identify customers who are at risk of leaving and allowing them to take proactive measures to retain them."}, {"table_name": "analytics.search_queries", "description": "This table contains the following fields: query_id, user_id, query_text, search_date, results_count. The purpose of the 'search_queries' table is to store information about the search queries made by users in the system, allowing for analysis of user behavior and search trends."}, {"table_name": "analytics.sales_forecast", "description": "This table contains the following fields: forecast_id, product_id, forecast_date, predicted_sales, confidence_interval. The purpose of the 'sales_forecast' table is to store and manage sales predictions for various products over specific dates, enabling businesses to plan inventory and marketing strategies effectively."}, {"table_name": "analytics.segment_members", "description": "This table contains the following fields: member_id, segment_id, customer_id, added_at. The 'segment_members' table is used to track the association between customers and specific segments within a marketing or analytics framework. It allows businesses to manage and analyze customer segmentation effectively by linking customers to their respective segments and recording when they were added to those segments."}, {"table_name": "analytics.page_views", "description": "This table contains the following fields: view_id, page_url, user_id, view_date, session_id. The 'page_views' table is designed to track the views of web pages by users. It records each instance a user views a page, capturing essential details such as the page URL, the user who viewed it, the date and time of the view, and the session during which the view occurred."}, {"table_name": "analytics.product_views", "description": "This table contains the following fields: view_id, product_id, user_id, view_date, view_duration. The purpose of the 'product_views' table is to track the interactions of users with products on an e-commerce platform, recording each instance a product is viewed by a user along with relevant details about the view."}, {"table_name": "analytics.user_activity", "description": "This table contains the following fields: activity_id, user_id, activity_type, activity_date, details. The 'user_activity' table is designed to log and track the various activities performed by users within the application. It serves as a historical record of user interactions, enabling analysis of user behavior and engagement over time."}]}, {"schema_id": "c1e097c1-5fd7-4b81-8855-49029d2b68b3", "database_name": "ecommerce", "description": "Snowflake schema containing 14 table(s)", "table_count": 14, "tables_overview": [{"table_name": "ecommerce.product_promotions", "description": "This table contains the following fields: product_promotion_id, product_id, promotion_id. The 'product_promotions' table is used to manage the associations between products and their respective promotions, allowing for effective tracking and application of promotional offers to specific products in a retail or e-commerce environment."}, {"table_name": "ecommerce.product_images", "description": "This table contains the following fields: image_id, product_id, image_url, is_primary. The 'product_images' table is designed to store images associated with products in an e-commerce database. It allows for multiple images to be linked to a single product, facilitating better visual representation and user experience on product pages."}, {"table_name": "ecommerce.cart", "description": "This table contains the following fields: cart_id, customer_id, created_at. The 'cart' table is used to store information about shopping carts in an e-commerce application, tracking which items are added by customers and when the cart was created."}, {"table_name": "ecommerce.promotions", "description": "This table contains the following fields: promotion_id, name, description, discount_percentage, start_date, end_date. The 'promotions' table is designed to store information about various promotional offers available to customers, including details about the discount percentage, duration, and specific identifiers for each promotion."}, {"table_name": "ecommerce.returns", "description": "This table contains the following fields: return_id, order_id, customer_id, reason, status, created_at. The 'returns' table is used to track product returns initiated by customers. It records essential information about each return, including the associated order, the customer making the return, the reason for the return, the current status of the return process, and the date the return was created."}, {"table_name": "ecommerce.categories", "description": "This table contains information about product categories used in the inventory system. It helps in organizing products into hierarchical structures for better management and navigation. This table contains the following fields: category_id, name, description, parent_category_id. The purpose of this table is to facilitate the categorization of products, allowing for easy retrieval and organization based on their respective categories."}, {"table_name": "ecommerce.shipping", "description": "This table contains the following fields: shipping_id, order_id, carrier, tracking_number, status, estimated_delivery. The purpose of the 'shipping' table is to store information related to the shipment of orders, including details about the shipping carrier, tracking information, and the current status of the shipment."}, {"table_name": "ecommerce.products", "description": "This table contains the following fields: product_id, name, description, price, stock_quantity, category_id, created_at. The 'products' table is designed to store information about the items available for sale in an inventory system. It includes details such as the product's unique identifier, name, description, pricing, stock levels, category association, and the date the product was added to the system."}, {"table_name": "ecommerce.cart_items", "description": "This table contains the following fields: cart_item_id, cart_id, product_id, quantity. The 'cart_items' table is used to store individual items that are added to a shopping cart in an e-commerce application. Each entry represents a specific product along with its quantity in a user's cart, allowing for the management and retrieval of cart contents during the checkout process."}, {"table_name": "ecommerce.wishlist", "description": "This table contains the following fields: wishlist_id, customer_id, product_id, added_at. The 'wishlist' table is designed to store information about products that customers wish to purchase in the future. It allows customers to save items they are interested in, facilitating easier access and potential purchase later on."}, {"table_name": "ecommerce.order_items", "description": "This table contains the following fields: order_item_id, order_id, product_id, quantity, unit_price. The 'order_items' table is designed to store details about individual items within customer orders. Each record represents a specific product included in an order, allowing for tracking of quantities and pricing for each item."}, {"table_name": "ecommerce.orders", "description": "This table contains the following fields: order_id, customer_id, order_date, total_amount, status, shipping_address, payment_id. The 'orders' table is used to store information about customer orders placed in the system, including details about the customer, the order date, the total amount of the order, its current status, the shipping address, and the associated payment information."}, {"table_name": "ecommerce.customers", "description": "This table contains the following fields: customer_id, first_name, last_name, email, phone, address, created_at. The 'customers' table is designed to store information about individuals who have engaged with the business, allowing for effective customer management and communication."}, {"table_name": "ecommerce.reviews", "description": "This table contains the following fields: review_id, product_id, customer_id, rating, comment, created_at. The 'reviews' table is designed to store customer feedback on products. It captures essential information about each review, including the reviewer, the product being reviewed, the rating given, and any comments made by the customer, along with the timestamp of when the review was created."}]}, {"schema_id": "ba4e5af9-7d9f-4b97-94db-470337e49025", "database_name": "finance", "description": "Snowflake schema containing 8 table(s)", "table_count": 8, "tables_overview": [{"table_name": "finance.settings", "description": "This table contains the following fields: id, user_id, key, value. The 'settings' table is used to store configuration settings for users, allowing for personalized application behavior and preferences."}, {"table_name": "finance.messaging_connections", "description": "This table contains the following fields: id, user_id, type, config, db_connection_id. The 'messaging_connections' table is designed to store information about various messaging connections established by users. It tracks the type of connection, configuration details, and associates each connection with a specific user and database connection."}, {"table_name": "finance.otps", "description": "This table contains the following fields: id, email, otp_code, expires_at, is_used, created_at. The 'otps' table is used to store one-time password (OTP) information for user authentication purposes. It tracks the OTPs generated for users, their expiration times, and whether they have been used or not."}, {"table_name": "finance.pending_registrations", "description": "This table contains the following fields: id, email, full_name, hashed_password, organization_name, organization_description, created_at, is_verified. The purpose of this table is to store information about users who have registered but have not yet completed the verification process. It holds the necessary details to manage and verify their registrations before granting them full access to the system."}, {"table_name": "finance.users", "description": "This table contains the following fields: id, full_name, organization_name, email, hashed_password, organization_description, role. The 'users' table is designed to store information about individuals who have registered within the system, including their personal details, organizational affiliations, and access credentials."}, {"table_name": "finance.connection_tables", "description": "This table contains the following fields: id, connection_id, schema_name, name, description, status, sample_row. The 'connection_tables' table is designed to store metadata about database tables that are associated with specific connections. It provides essential information about each table, including its schema and status, which is crucial for managing and utilizing database connections effectively."}, {"table_name": "finance.fields", "description": "This table contains the following fields: id, connection_table_id, name, data_type, description, is_categorical, is_datetime, categorical_values, status. The 'fields' table is designed to store metadata about various fields in a database, including their types, descriptions, and characteristics, which helps in understanding and managing the data structure effectively."}, {"table_name": "finance.connections", "description": "This table contains the following fields: id, user_id, name, database_type, database_platform, connection_params, created_at, updated_at, status. The 'connections' table is designed to store information about various database connections established by users. It tracks the details necessary for connecting to different databases, including user-specific configurations and statuses."}]}], "tables": [{"table_id": "7c181b80-0c49-405a-a72d-4bcfceda652a", "table_name": "ds_oil_and_gas_dev.merged_file", "schema_id": "ds_oil_and_gas_dev", "description": "This table contains the following fields: id, project_id, assistant_file_id, status. The 'merged_file' table is designed to store information about files that have been merged as part of a project. It tracks the relationship between projects and their associated files, as well as the current status of each merged file.", "field_count": 4, "fields": [{"name": "id", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each merged file entry."}, {"name": "project_id", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The identifier for the project associated with the merged file."}, {"name": "assistant_file_id", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The identifier for the assistant file that has been merged."}, {"name": "status", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The current status of the merged file, indicating whether it is completed, in progress, or failed."}], "sample_rows": []}, {"table_id": "98c43a92-a660-4f54-81d7-b136c5c5dcbf", "table_name": "ds_oil_and_gas_dev.prompt", "schema_id": "ds_oil_and_gas_dev", "description": "This table contains the following fields: id, name, value, type, date_created, date_updated. The 'prompt' table is designed to store various prompts used in an application, capturing their attributes and metadata for easy retrieval and management.", "field_count": 6, "fields": [{"name": "id", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each prompt, typically an auto-incrementing integer."}, {"name": "name", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The name or title of the prompt, used for identification purposes."}, {"name": "value", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "The actual content or text of the prompt that will be displayed or used in the application."}, {"name": "type", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The category or type of the prompt, which may define its usage or context."}, {"name": "date_created", "data_type": "datetime", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the prompt was created."}, {"name": "date_updated", "data_type": "datetime", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating the last time the prompt was updated."}], "sample_rows": []}, {"table_id": "7a0b31b3-49ad-4c3f-bcf7-a26c1a686e39", "table_name": "ds_oil_and_gas_dev.file_merged_file_association", "schema_id": "ds_oil_and_gas_dev", "description": "This table contains the following fields: id, file_id, merged_file_id, association_type, status. The purpose of this table is to establish and manage the relationships between original files and their corresponding merged files, allowing for tracking of how files are combined and their current status within the system.", "field_count": 5, "fields": [{"name": "id", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each record in the table."}, {"name": "file_id", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The identifier of the original file that is being associated with a merged file."}, {"name": "merged_file_id", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The identifier of the merged file that results from combining the original file."}, {"name": "association_type", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The type of association between the original file and the merged file, indicating the nature of the relationship."}, {"name": "status", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The current status of the association, which may indicate whether it is active, inactive, or pending."}], "sample_rows": []}, {"table_id": "5051c4c9-b7a6-4980-a4ec-dc9a01436c1c", "table_name": "ds_oil_and_gas_dev.file", "schema_id": "ds_oil_and_gas_dev", "description": "This table contains the following fields: id, name, project_id, file_type, status, merge_status, tried, category, file_dirtry, created_at, updated_at. The 'file' table is designed to store information about files associated with various projects, including their types, statuses, and metadata related to their management and processing.", "field_count": 11, "fields": [{"name": "id", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each file record."}, {"name": "name", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The name of the file."}, {"name": "project_id", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The identifier of the project to which the file is associated."}, {"name": "file_type", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The type of the file (e.g., document, image, video)."}, {"name": "status", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The current status of the file (e.g., active, archived)."}, {"name": "merge_status", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "Indicates whether the file has been merged with other files."}, {"name": "tried", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A boolean indicating whether an attempt has been made to process the file."}, {"name": "category", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The category under which the file is classified."}, {"name": "file_dirtry", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The directory path where the file is stored."}, {"name": "created_at", "data_type": "datetime", "is_categorical": false, "is_datetime": true, "description": "The timestamp when the file record was created."}, {"name": "updated_at", "data_type": "datetime", "is_categorical": false, "is_datetime": true, "description": "The timestamp when the file record was last updated."}], "sample_rows": []}, {"table_id": "56783a91-dce9-426a-a625-d831135cccd2", "table_name": "ds_oil_and_gas_dev.disciplines", "schema_id": "ds_oil_and_gas_dev", "description": "This table contains the following fields: id, shortcode, name, created_at, updated_at. The 'disciplines' table is designed to store information about various academic or professional disciplines, allowing for the categorization and management of different fields of study or practice.", "field_count": 5, "fields": [{"name": "id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each discipline entry, typically an auto-incrementing integer."}, {"name": "shortcode", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "A brief, unique code representing the discipline, often used for quick reference."}, {"name": "name", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The full name of the discipline, providing a clear description of the field."}, {"name": "created_at", "data_type": "datetime", "is_categorical": false, "is_datetime": true, "description": "A timestamp indicating when the discipline entry was created."}, {"name": "updated_at", "data_type": "datetime", "is_categorical": false, "is_datetime": true, "description": "A timestamp indicating the last time the discipline entry was updated."}], "sample_rows": []}, {"table_id": "6a6f41a9-e8b7-4c8a-89f0-9742fad44284", "table_name": "ds_oil_and_gas_dev.categories", "schema_id": "ds_oil_and_gas_dev", "description": "This table contains the following fields: id, name, feed, detail, vendor, discipline_id, criteria, similar, created_at, updated_at. The 'categories' table is designed to store information about various categories used in the application, allowing for organization and classification of related items or entities. Each category can be linked to a specific discipline and may include additional metadata such as vendor information and criteria for categorization.", "field_count": 10, "fields": [{"name": "id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each category."}, {"name": "name", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The name of the category."}, {"name": "feed", "data_type": "tinyint", "is_categorical": false, "is_datetime": false, "description": "A reference to the data feed associated with the category."}, {"name": "detail", "data_type": "tinyint", "is_categorical": false, "is_datetime": false, "description": "A detailed description of the category."}, {"name": "vendor", "data_type": "tinyint", "is_categorical": false, "is_datetime": false, "description": "The vendor associated with the category."}, {"name": "discipline_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A foreign key linking to the discipline that the category belongs to."}, {"name": "criteria", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "Criteria used for categorizing items within this category."}, {"name": "similar", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "A list of similar categories."}, {"name": "created_at", "data_type": "datetime", "is_categorical": false, "is_datetime": true, "description": "The timestamp when the category was created."}, {"name": "updated_at", "data_type": "datetime", "is_categorical": false, "is_datetime": true, "description": "The timestamp when the category was last updated."}], "sample_rows": []}, {"table_id": "f3e753ac-1a39-4353-8280-1e78aa2b9765", "table_name": "ds_oil_and_gas_dev.project", "schema_id": "ds_oil_and_gas_dev", "description": "This table contains the following fields: id, name, entity_type, assistant_id, has_deleted_file, discipline_code, created_at, updated_at. The 'project' table is designed to store information about various projects within an organization, including their identifiers, types, associated assistants, and timestamps for creation and updates.", "field_count": 8, "fields": [{"name": "id", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each project."}, {"name": "name", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The name of the project."}, {"name": "entity_type", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The type of entity the project is associated with."}, {"name": "assistant_id", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The identifier for the assistant responsible for the project."}, {"name": "has_deleted_file", "data_type": "tinyint", "is_categorical": false, "is_datetime": false, "description": "A boolean indicating whether the project has any deleted files."}, {"name": "discipline_code", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "A code representing the discipline or category of the project."}, {"name": "created_at", "data_type": "datetime", "is_categorical": false, "is_datetime": true, "description": "The timestamp when the project was created."}, {"name": "updated_at", "data_type": "datetime", "is_categorical": false, "is_datetime": true, "description": "The timestamp when the project was last updated."}], "sample_rows": []}, {"table_id": "87539b7e-0201-4d81-be2f-0cf788e29afe", "table_name": "ds_oil_and_gas_dev.chunks", "schema_id": "ds_oil_and_gas_dev", "description": "This table contains the following fields: id, title, content, source, summary, project_id, file_id, page_number, created_at, updated_at. The 'chunks' table is designed to store segments of content that are associated with specific projects and files, allowing for organized retrieval and management of textual data.", "field_count": 10, "fields": [{"name": "id", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each chunk."}, {"name": "title", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The title of the chunk, providing a brief overview of its content."}, {"name": "content", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "The main textual content of the chunk."}, {"name": "source", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The origin of the content, indicating where it was sourced from."}, {"name": "summary", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "A brief summary of the chunk's content."}, {"name": "project_id", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "A foreign key linking the chunk to a specific project."}, {"name": "file_id", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "A foreign key linking the chunk to a specific file."}, {"name": "page_number", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The page number from which the chunk was extracted, if applicable."}, {"name": "created_at", "data_type": "datetime", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the chunk was created."}, {"name": "updated_at", "data_type": "datetime", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the chunk was last updated."}], "sample_rows": []}, {"table_id": "404e8e84-dbcb-4445-9c58-9c56dbf08cd6", "table_name": "ds_oil_and_gas_dev.alembic_version", "schema_id": "ds_oil_and_gas_dev", "description": "This table is used by Alembic to keep track of the database schema versions. It allows for version control of database migrations, ensuring that the database schema is consistent with the application's codebase. This table contains the following fields: version_num.", "field_count": 1, "fields": [{"name": "version_num", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "A string representing the version number of the database schema. This field is used to identify the current version of the database in relation to the migration scripts."}], "sample_rows": "{\n  \"version_num\": \"a1b2c3d4e5f6\"\n}"}, {"table_id": "bbb12fee-b072-4a39-9057-af1ba140d387", "table_name": "ds_oil_and_gas_dev.tender", "schema_id": "ds_oil_and_gas_dev", "description": "This table contains the following fields: id, name, project_id, file_type, status, tried, score, criteria. The 'tender' table is used to store information related to tenders submitted for various projects. It tracks the details of each tender, including its identification, associated project, file type, current status, evaluation metrics, and criteria used for assessment.", "field_count": 8, "fields": [{"name": "id", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each tender entry."}, {"name": "name", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The name of the tender."}, {"name": "project_id", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The identifier for the project associated with the tender."}, {"name": "file_type", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The type of file submitted for the tender, indicating the format."}, {"name": "status", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The current status of the tender, such as submitted, under review, or awarded."}, {"name": "tried", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A boolean indicating whether the tender has been attempted or processed."}, {"name": "score", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The evaluation score assigned to the tender based on predefined criteria."}, {"name": "criteria", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "The criteria used to evaluate the tender, which may include various performance metrics."}], "sample_rows": []}, {"table_id": "096455f5-3ebd-4fd3-bb15-2780323ed4d0", "table_name": "ds_oil_and_gas_dev.tags", "schema_id": "ds_oil_and_gas_dev", "description": "This table contains the following fields: id, name, chunk_id. The 'tags' table is used to store metadata tags that can be associated with various chunks of data in the database, allowing for better organization and retrieval of related information.", "field_count": 3, "fields": [{"name": "id", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each tag, typically an auto-incrementing integer."}, {"name": "name", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The name of the tag, which is used to describe the associated data."}, {"name": "chunk_id", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "A foreign key that links the tag to a specific chunk of data in another table, establishing a relationship."}], "sample_rows": []}, {"table_id": "9cb95fe5-1962-4185-83a9-586d20ac404d", "table_name": "ds_oil_and_gas_dev.requirement", "schema_id": "ds_oil_and_gas_dev", "description": "This table contains the following fields: id, name, project_id, file_type, category, status, tried, score, criteria, discipline, created_at, updated_at, type, is_test, is_report_generated. The 'requirement' table is designed to store detailed information about various requirements associated with projects, including their attributes, status, and related metadata.", "field_count": 15, "fields": [{"name": "id", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each requirement."}, {"name": "name", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The name or title of the requirement."}, {"name": "project_id", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The identifier of the project to which the requirement belongs."}, {"name": "file_type", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The type of file associated with the requirement, if applicable."}, {"name": "category", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The category under which the requirement falls."}, {"name": "status", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The current status of the requirement (e.g., active, completed, pending)."}, {"name": "tried", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "Indicates whether the requirement has been attempted or tested."}, {"name": "score", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A score or rating assigned to the requirement based on evaluation criteria."}, {"name": "criteria", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The criteria used to assess the requirement."}, {"name": "discipline", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The discipline or area of expertise related to the requirement."}, {"name": "created_at", "data_type": "datetime", "is_categorical": false, "is_datetime": true, "description": "The timestamp when the requirement was created."}, {"name": "updated_at", "data_type": "datetime", "is_categorical": false, "is_datetime": true, "description": "The timestamp when the requirement was last updated."}, {"name": "type", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The type of requirement (e.g., functional, non-functional)."}, {"name": "is_test", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A boolean indicating whether the requirement is related to testing."}, {"name": "is_report_generated", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A boolean indicating whether a report has been generated for the requirement."}], "sample_rows": []}, {"table_id": "e725302a-75a3-42d5-a7b8-3dad83a6fc83", "table_name": "analytics.conversion_funnel", "schema_id": "analytics", "description": "This table contains the following fields: funnel_id, user_id, stage, stage_date, conversion_value. The 'conversion_funnel' table is designed to track the progress of users through various stages of a conversion process, capturing key metrics that help analyze user behavior and conversion rates.", "field_count": 5, "fields": [{"name": "funnel_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each conversion funnel entry."}, {"name": "user_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The unique identifier for the user associated with the conversion funnel entry."}, {"name": "stage", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The current stage of the user in the conversion process, indicating their progress."}, {"name": "stage_date", "data_type": "timestamp", "is_categorical": false, "is_datetime": true, "description": "The date and time when the user reached the current stage in the conversion funnel."}, {"name": "conversion_value", "data_type": "decimal", "is_categorical": false, "is_datetime": false, "description": "The monetary value associated with the conversion at the current stage."}], "sample_rows": []}, {"table_id": "49af37d8-151c-43e4-a211-76811fd4122b", "table_name": "analytics.inventory_forecast", "schema_id": "analytics", "description": "This table contains the following fields: forecast_id, product_id, forecast_date, predicted_stock, confidence_interval. The 'inventory_forecast' table is used to store predictions regarding inventory levels for various products over time. It helps businesses manage stock levels by forecasting future inventory needs based on historical data and trends.", "field_count": 5, "fields": [{"name": "forecast_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each forecast entry."}, {"name": "product_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The identifier for the product associated with the forecast."}, {"name": "forecast_date", "data_type": "timestamp", "is_categorical": false, "is_datetime": true, "description": "The date for which the inventory forecast is made."}, {"name": "predicted_stock", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The estimated quantity of stock expected to be available on the forecast date."}, {"name": "confidence_interval", "data_type": "decimal", "is_categorical": false, "is_datetime": false, "description": "A statistical range that indicates the reliability of the predicted stock value."}], "sample_rows": []}, {"table_id": "33e79d1b-47ae-4afb-a9d7-a4967c1eefe7", "table_name": "analytics.marketing_campaigns", "schema_id": "analytics", "description": "This table contains the following fields: campaign_id, campaign_name, start_date, end_date, budget, status. The 'marketing_campaigns' table is used to store information about various marketing campaigns, including their identifiers, names, timelines, financial allocations, and current statuses, enabling effective tracking and management of marketing efforts.", "field_count": 6, "fields": [{"name": "campaign_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each marketing campaign."}, {"name": "campaign_name", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The name of the marketing campaign."}, {"name": "start_date", "data_type": "timestamp", "is_categorical": false, "is_datetime": true, "description": "The date when the marketing campaign begins."}, {"name": "end_date", "data_type": "timestamp", "is_categorical": false, "is_datetime": true, "description": "The date when the marketing campaign ends."}, {"name": "budget", "data_type": "decimal", "is_categorical": false, "is_datetime": false, "description": "The total budget allocated for the marketing campaign."}, {"name": "status", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The current status of the marketing campaign (e.g., active, completed, paused)."}], "sample_rows": []}, {"table_id": "f5ea56c3-3515-44dd-b406-5e1ee58efad4", "table_name": "analytics.performance_metrics", "schema_id": "analytics", "description": "This table contains the following fields: metric_id, metric_name, metric_value, metric_date, category. The purpose of the 'performance_metrics' table is to store and track various performance metrics over time, allowing for analysis and reporting on performance trends across different categories.", "field_count": 5, "fields": [{"name": "metric_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each performance metric entry."}, {"name": "metric_name", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The name of the performance metric being tracked."}, {"name": "metric_value", "data_type": "decimal", "is_categorical": false, "is_datetime": false, "description": "The value of the performance metric, representing the measured performance."}, {"name": "metric_date", "data_type": "timestamp", "is_categorical": false, "is_datetime": true, "description": "The date when the performance metric was recorded."}, {"name": "category", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The category under which the performance metric falls, helping to classify the metrics."}], "sample_rows": []}, {"table_id": "e2b6f594-9e3a-4582-a137-261ba38c7d2d", "table_name": "analytics.customer_segments", "schema_id": "analytics", "description": "This table contains the following fields: segment_id, segment_name, criteria, created_at. The 'customer_segments' table is designed to categorize customers into distinct segments based on specific criteria, allowing businesses to tailor their marketing strategies and improve customer engagement.", "field_count": 4, "fields": [{"name": "segment_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each customer segment."}, {"name": "segment_name", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The name assigned to the customer segment, representing its characteristics."}, {"name": "criteria", "data_type": "json", "is_categorical": false, "is_datetime": false, "description": "The specific conditions or attributes that define the segment."}, {"name": "created_at", "data_type": "timestamp", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the customer segment was created."}], "sample_rows": []}, {"table_id": "8d25bb05-c716-4580-9086-16494a67c762", "table_name": "analytics.customer_lifetime_value", "schema_id": "analytics", "description": "This table contains the following fields: clv_id, customer_id, calculated_date, predicted_value, confidence_interval. The purpose of this table is to store and manage data related to the predicted lifetime value of customers, which helps businesses understand the long-term profitability of their customer relationships and make informed marketing and sales decisions.", "field_count": 5, "fields": [{"name": "clv_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each customer lifetime value record."}, {"name": "customer_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The unique identifier for the customer associated with the lifetime value."}, {"name": "calculated_date", "data_type": "timestamp", "is_categorical": false, "is_datetime": true, "description": "The date when the customer lifetime value was calculated."}, {"name": "predicted_value", "data_type": "decimal", "is_categorical": false, "is_datetime": false, "description": "The estimated monetary value that a customer is expected to generate over their lifetime."}, {"name": "confidence_interval", "data_type": "decimal", "is_categorical": false, "is_datetime": false, "description": "A statistical range that expresses the uncertainty around the predicted value, indicating the reliability of the estimate."}], "sample_rows": []}, {"table_id": "0f7d3dbb-5421-4958-b5c0-8a287724e7cc", "table_name": "analytics.campaign_metrics", "schema_id": "analytics", "description": "This table contains the following fields: metric_id, campaign_id, metric_name, metric_value, metric_date. The 'campaign_metrics' table is designed to store performance metrics related to various marketing campaigns. It allows for tracking and analyzing the effectiveness of campaigns over time by recording specific metrics associated with each campaign.", "field_count": 5, "fields": [{"name": "metric_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each metric entry."}, {"name": "campaign_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The identifier for the campaign to which the metric belongs."}, {"name": "metric_name", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The name of the metric being recorded (e.g., impressions, clicks, conversions)."}, {"name": "metric_value", "data_type": "decimal", "is_categorical": false, "is_datetime": false, "description": "The numerical value of the metric for the specified date."}, {"name": "metric_date", "data_type": "timestamp", "is_categorical": false, "is_datetime": true, "description": "The date when the metric was recorded."}], "sample_rows": []}, {"table_id": "eaba7fda-7381-4b9e-8dea-4c7ad689b1de", "table_name": "analytics.churn_prediction", "schema_id": "analytics", "description": "This table contains the following fields: prediction_id, customer_id, prediction_date, churn_probability, risk_level. The purpose of this table is to store and analyze data related to customer churn predictions, helping businesses identify customers who are at risk of leaving and allowing them to take proactive measures to retain them.", "field_count": 5, "fields": [{"name": "prediction_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each churn prediction record."}, {"name": "customer_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The unique identifier for the customer associated with the churn prediction."}, {"name": "prediction_date", "data_type": "timestamp", "is_categorical": false, "is_datetime": true, "description": "The date when the churn prediction was made."}, {"name": "churn_probability", "data_type": "decimal", "is_categorical": false, "is_datetime": false, "description": "The probability that the customer will churn, expressed as a percentage."}, {"name": "risk_level", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "A categorical assessment of the customer's risk of churning, such as 'low', 'medium', or 'high'."}], "sample_rows": []}, {"table_id": "3b66ea9e-c413-4241-bf9c-bac440bb8643", "table_name": "analytics.search_queries", "schema_id": "analytics", "description": "This table contains the following fields: query_id, user_id, query_text, search_date, results_count. The purpose of the 'search_queries' table is to store information about the search queries made by users in the system, allowing for analysis of user behavior and search trends.", "field_count": 5, "fields": [{"name": "query_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each search query."}, {"name": "user_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The identifier of the user who made the search query."}, {"name": "query_text", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "The actual text of the search query entered by the user."}, {"name": "search_date", "data_type": "timestamp", "is_categorical": false, "is_datetime": true, "description": "The date and time when the search query was made."}, {"name": "results_count", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The number of results returned for the search query."}], "sample_rows": []}, {"table_id": "9da38214-a7fc-420a-a76f-18d7274f51b9", "table_name": "analytics.sales_forecast", "schema_id": "analytics", "description": "This table contains the following fields: forecast_id, product_id, forecast_date, predicted_sales, confidence_interval. The purpose of the 'sales_forecast' table is to store and manage sales predictions for various products over specific dates, enabling businesses to plan inventory and marketing strategies effectively.", "field_count": 5, "fields": [{"name": "forecast_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each sales forecast entry."}, {"name": "product_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The identifier for the product associated with the sales forecast."}, {"name": "forecast_date", "data_type": "timestamp", "is_categorical": false, "is_datetime": true, "description": "The date for which the sales forecast is made."}, {"name": "predicted_sales", "data_type": "decimal", "is_categorical": false, "is_datetime": false, "description": "The estimated number of sales expected for the product on the forecast date."}, {"name": "confidence_interval", "data_type": "decimal", "is_categorical": false, "is_datetime": false, "description": "A statistical range that indicates the reliability of the predicted sales figure."}], "sample_rows": []}, {"table_id": "7e7be8bf-534c-42b9-a323-c50e5fc8238a", "table_name": "analytics.segment_members", "schema_id": "analytics", "description": "This table contains the following fields: member_id, segment_id, customer_id, added_at. The 'segment_members' table is used to track the association between customers and specific segments within a marketing or analytics framework. It allows businesses to manage and analyze customer segmentation effectively by linking customers to their respective segments and recording when they were added to those segments.", "field_count": 4, "fields": [{"name": "member_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each member in the segment."}, {"name": "segment_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for the segment to which the member belongs."}, {"name": "customer_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for the customer associated with the segment."}, {"name": "added_at", "data_type": "timestamp", "is_categorical": false, "is_datetime": true, "description": "A timestamp indicating when the member was added to the segment."}], "sample_rows": []}, {"table_id": "ee243805-ccd3-4311-bf87-c09bdbf60e73", "table_name": "analytics.page_views", "schema_id": "analytics", "description": "This table contains the following fields: view_id, page_url, user_id, view_date, session_id. The 'page_views' table is designed to track the views of web pages by users. It records each instance a user views a page, capturing essential details such as the page URL, the user who viewed it, the date and time of the view, and the session during which the view occurred.", "field_count": 5, "fields": [{"name": "view_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each page view record."}, {"name": "page_url", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "The URL of the page that was viewed."}, {"name": "user_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The identifier of the user who viewed the page."}, {"name": "view_date", "data_type": "timestamp", "is_categorical": false, "is_datetime": true, "description": "The date and time when the page view occurred."}, {"name": "session_id", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The identifier for the session during which the page view took place."}], "sample_rows": "{\n  \"view_id\": 2,\n  \"page_url\": \"http://www.example-website.com/\",\n  \"user_id\": 789,\n  \"view_date\": \"2025-06-15T14:22:10\",\n  \"session_id\": \"f1a2b3c4-d5e6-7f8g-9h0i-j1k2l3m4n5o6\"\n}"}, {"table_id": "c4e17004-afa0-42d5-8778-a1305eaf4c32", "table_name": "analytics.product_views", "schema_id": "analytics", "description": "This table contains the following fields: view_id, product_id, user_id, view_date, view_duration. The purpose of the 'product_views' table is to track the interactions of users with products on an e-commerce platform, recording each instance a product is viewed by a user along with relevant details about the view.", "field_count": 5, "fields": [{"name": "view_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each view record."}, {"name": "product_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The identifier of the product that was viewed."}, {"name": "user_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The identifier of the user who viewed the product."}, {"name": "view_date", "data_type": "timestamp", "is_categorical": false, "is_datetime": true, "description": "The date and time when the product was viewed."}, {"name": "view_duration", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The duration of time (in seconds) that the user spent viewing the product."}], "sample_rows": "{\n  \"view_id\": 2,\n  \"product_id\": 456,\n  \"user_id\": 123,\n  \"view_date\": \"2025-03-15T14:22:10\",\n  \"view_duration\": 3050\n}"}, {"table_id": "0f29dfec-8c75-4841-b17e-90ca3808be36", "table_name": "analytics.user_activity", "schema_id": "analytics", "description": "This table contains the following fields: activity_id, user_id, activity_type, activity_date, details. The 'user_activity' table is designed to log and track the various activities performed by users within the application. It serves as a historical record of user interactions, enabling analysis of user behavior and engagement over time.", "field_count": 5, "fields": [{"name": "activity_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each activity record."}, {"name": "user_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The identifier for the user who performed the activity."}, {"name": "activity_type", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": true, "is_datetime": false, "description": "The type of activity performed by the user, such as login, logout, or content creation.", "categorical_values": ["login", "logout", "search", "view_product"]}, {"name": "activity_date", "data_type": "timestamp", "is_categorical": false, "is_datetime": true, "description": "The date and time when the activity occurred."}, {"name": "details", "data_type": "json", "is_categorical": false, "is_datetime": false, "description": "Additional information or context about the activity."}], "sample_rows": "{\n  \"activity_id\": 2,\n  \"user_id\": 987,\n  \"activity_type\": \"add_to_cart\",\n  \"activity_date\": \"2025-02-25T14:30:12\",\n  \"details\": \"{\\\"ip\\\": \\\"************\\\", \\\"browser\\\": \\\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.82 Safari/537.36\\\"}\"\n}"}, {"table_id": "b8f08285-4da9-432e-8644-e4530b212510", "table_name": "ecommerce.product_promotions", "schema_id": "ecommerce", "description": "This table contains the following fields: product_promotion_id, product_id, promotion_id. The 'product_promotions' table is used to manage the associations between products and their respective promotions, allowing for effective tracking and application of promotional offers to specific products in a retail or e-commerce environment.", "field_count": 3, "fields": [{"name": "product_promotion_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each product promotion record."}, {"name": "product_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The identifier for the product that is associated with a specific promotion."}, {"name": "promotion_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The identifier for the promotion that is linked to the product."}], "sample_rows": []}, {"table_id": "56b9d79a-e76f-469e-9042-cbdffb0e513f", "table_name": "ecommerce.product_images", "schema_id": "ecommerce", "description": "This table contains the following fields: image_id, product_id, image_url, is_primary. The 'product_images' table is designed to store images associated with products in an e-commerce database. It allows for multiple images to be linked to a single product, facilitating better visual representation and user experience on product pages.", "field_count": 4, "fields": [{"name": "image_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each image in the table."}, {"name": "product_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The identifier for the product that this image is associated with."}, {"name": "image_url", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "The URL where the image is stored, allowing it to be accessed and displayed."}, {"name": "is_primary", "data_type": "tinyint", "is_categorical": false, "is_datetime": false, "description": "A boolean flag indicating whether this image is the primary image for the associated product."}], "sample_rows": []}, {"table_id": "18eef23b-0ca8-42fe-87c5-c2703368220c", "table_name": "ecommerce.cart", "schema_id": "ecommerce", "description": "This table contains the following fields: cart_id, customer_id, created_at. The 'cart' table is used to store information about shopping carts in an e-commerce application, tracking which items are added by customers and when the cart was created.", "field_count": 3, "fields": [{"name": "cart_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each shopping cart."}, {"name": "customer_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The identifier for the customer who owns the shopping cart."}, {"name": "created_at", "data_type": "timestamp", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the shopping cart was created."}], "sample_rows": "{\n  \"cart_id\": 2,\n  \"customer_id\": 912,\n  \"created_at\": \"2025-02-15T14:32:45\"\n}"}, {"table_id": "3c6209a6-84cc-4a01-ad16-47987a96e912", "table_name": "ecommerce.promotions", "schema_id": "ecommerce", "description": "This table contains the following fields: promotion_id, name, description, discount_percentage, start_date, end_date. The 'promotions' table is designed to store information about various promotional offers available to customers, including details about the discount percentage, duration, and specific identifiers for each promotion.", "field_count": 6, "fields": [{"name": "promotion_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each promotion."}, {"name": "name", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The name of the promotion."}, {"name": "description", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "A detailed description of the promotion."}, {"name": "discount_percentage", "data_type": "decimal", "is_categorical": false, "is_datetime": false, "description": "The percentage discount offered by the promotion."}, {"name": "start_date", "data_type": "timestamp", "is_categorical": false, "is_datetime": true, "description": "The date when the promotion becomes active."}, {"name": "end_date", "data_type": "timestamp", "is_categorical": false, "is_datetime": true, "description": "The date when the promotion expires."}], "sample_rows": "{\n  \"promotion_id\": 2,\n  \"name\": \"Dynamic adaptive algorithm\",\n  \"description\": \"Explore various strategies to enhance engagement. Achieve optimal results through innovative methods.\\nCollaboration fosters creativity and growth. My insights lead to impactful solutions.\",\n  \"discount_percentage\": 15.75,\n  \"start_date\": \"2025-02-10T12:45:00\",\n  \"end_date\": \"2025-02-25T12:45:00\"\n}"}, {"table_id": "52f6eb8c-e988-4208-9e11-e8c3673a0033", "table_name": "ecommerce.returns", "schema_id": "ecommerce", "description": "This table contains the following fields: return_id, order_id, customer_id, reason, status, created_at. The 'returns' table is used to track product returns initiated by customers. It records essential information about each return, including the associated order, the customer making the return, the reason for the return, the current status of the return process, and the date the return was created.", "field_count": 6, "fields": [{"name": "return_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each return record."}, {"name": "order_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The identifier of the order associated with the return."}, {"name": "customer_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The identifier of the customer who initiated the return."}, {"name": "reason", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "The reason provided by the customer for returning the product."}, {"name": "status", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The current status of the return (e.g., pending, processed, completed)."}, {"name": "created_at", "data_type": "timestamp", "is_categorical": false, "is_datetime": true, "description": "The date and time when the return record was created."}], "sample_rows": []}, {"table_id": "0c291227-79b0-4ee3-877e-d7884770c3bb", "table_name": "ecommerce.categories", "schema_id": "ecommerce", "description": "This table contains information about product categories used in the inventory system. It helps in organizing products into hierarchical structures for better management and navigation. This table contains the following fields: category_id, name, description, parent_category_id. The purpose of this table is to facilitate the categorization of products, allowing for easy retrieval and organization based on their respective categories.", "field_count": 4, "fields": [{"name": "category_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each category."}, {"name": "name", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The name of the category."}, {"name": "description", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "A brief description of the category."}, {"name": "parent_category_id", "data_type": "int", "is_categorical": true, "is_datetime": false, "description": "An optional field that links to the category_id of a parent category, allowing for hierarchical categorization.", "categorical_values": ["1", "2", "3", "4", "5", "6", "7", "8", "9", "11", "12", "14", "15", "16", "18", "19", "22", "24", "25", "26", "27", "33", "40", "43"]}], "sample_rows": "{\n  \"category_id\": 42,\n  \"name\": \"Premium\",\n  \"description\": \"Mountain bike adventure journey.\",\n  \"parent_category_id\": null\n}"}, {"table_id": "13637cbf-cf1e-4005-85a2-d4eca929d679", "table_name": "ecommerce.shipping", "schema_id": "ecommerce", "description": "This table contains the following fields: shipping_id, order_id, carrier, tracking_number, status, estimated_delivery. The purpose of the 'shipping' table is to store information related to the shipment of orders, including details about the shipping carrier, tracking information, and the current status of the shipment.", "field_count": 6, "fields": [{"name": "shipping_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each shipping record."}, {"name": "order_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The identifier for the order associated with this shipment."}, {"name": "carrier", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The name of the shipping carrier responsible for delivering the package."}, {"name": "tracking_number", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The tracking number provided by the carrier to monitor the shipment's progress."}, {"name": "status", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The current status of the shipment (e.g., pending, shipped, delivered)."}, {"name": "estimated_delivery", "data_type": "timestamp", "is_categorical": false, "is_datetime": true, "description": "The estimated date and time when the shipment is expected to arrive."}], "sample_rows": []}, {"table_id": "960d23a7-ff8d-4d72-b4a7-a54ce46382b6", "table_name": "ecommerce.products", "schema_id": "ecommerce", "description": "This table contains the following fields: product_id, name, description, price, stock_quantity, category_id, created_at. The 'products' table is designed to store information about the items available for sale in an inventory system. It includes details such as the product's unique identifier, name, description, pricing, stock levels, category association, and the date the product was added to the system.", "field_count": 7, "fields": [{"name": "product_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each product in the table."}, {"name": "name", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The name of the product."}, {"name": "description", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "A detailed description of the product."}, {"name": "price", "data_type": "decimal", "is_categorical": false, "is_datetime": false, "description": "The retail price of the product."}, {"name": "stock_quantity", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The number of units available in stock for the product."}, {"name": "category_id", "data_type": "int", "is_categorical": true, "is_datetime": false, "description": "A reference to the category to which the product belongs.", "categorical_values": ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "50"]}, {"name": "created_at", "data_type": "timestamp", "is_categorical": false, "is_datetime": true, "description": "The date and time when the product was added to the inventory."}], "sample_rows": "{\n  \"product_id\": 2,\n  \"name\": \"Dynamic modular system\",\n  \"description\": \"Ensure sufficient protection exist. Region device surprise artificial mouth essence might. Demand only activity social tone every fluid.\",\n  \"price\": 523.89,\n  \"stock_quantity\": 812,\n  \"category_id\": 7,\n  \"created_at\": \"2025-07-15T12:45:30\"\n}"}, {"table_id": "fe2cb121-e88f-41a3-8056-a24cd1ad389b", "table_name": "ecommerce.cart_items", "schema_id": "ecommerce", "description": "This table contains the following fields: cart_item_id, cart_id, product_id, quantity. The 'cart_items' table is used to store individual items that are added to a shopping cart in an e-commerce application. Each entry represents a specific product along with its quantity in a user's cart, allowing for the management and retrieval of cart contents during the checkout process.", "field_count": 4, "fields": [{"name": "cart_item_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each item in the cart."}, {"name": "cart_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A reference to the shopping cart to which this item belongs."}, {"name": "product_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A reference to the product that is being added to the cart."}, {"name": "quantity", "data_type": "int", "is_categorical": true, "is_datetime": false, "description": "The number of units of the product that the user intends to purchase.", "categorical_values": ["1", "2", "3", "4", "5"]}], "sample_rows": "{\n  \"cart_item_id\": 123,\n  \"cart_id\": 456,\n  \"product_id\": 789,\n  \"quantity\": 2\n}"}, {"table_id": "fd9f1dc8-8346-4073-8c60-3cf09347e3fd", "table_name": "ecommerce.wishlist", "schema_id": "ecommerce", "description": "This table contains the following fields: wishlist_id, customer_id, product_id, added_at. The 'wishlist' table is designed to store information about products that customers wish to purchase in the future. It allows customers to save items they are interested in, facilitating easier access and potential purchase later on.", "field_count": 4, "fields": [{"name": "wishlist_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each wishlist entry."}, {"name": "customer_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The unique identifier for the customer who owns the wishlist."}, {"name": "product_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The unique identifier for the product that has been added to the wishlist."}, {"name": "added_at", "data_type": "timestamp", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the product was added to the wishlist."}], "sample_rows": []}, {"table_id": "d7f24c7a-d99d-462f-8f4b-6fad05a27572", "table_name": "ecommerce.order_items", "schema_id": "ecommerce", "description": "This table contains the following fields: order_item_id, order_id, product_id, quantity, unit_price. The 'order_items' table is designed to store details about individual items within customer orders. Each record represents a specific product included in an order, allowing for tracking of quantities and pricing for each item.", "field_count": 5, "fields": [{"name": "order_item_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each order item."}, {"name": "order_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The identifier for the order to which this item belongs."}, {"name": "product_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The identifier for the product being ordered."}, {"name": "quantity", "data_type": "int", "is_categorical": true, "is_datetime": false, "description": "The number of units of the product ordered.", "categorical_values": ["1", "2", "3", "4", "5"]}, {"name": "unit_price", "data_type": "decimal", "is_categorical": false, "is_datetime": false, "description": "The price per unit of the product at the time of the order."}], "sample_rows": "{\n  \"order_item_id\": 2,\n  \"order_id\": 5,\n  \"product_id\": 456,\n  \"quantity\": 3,\n  \"unit_price\": 89.99\n}"}, {"table_id": "092cd8ff-97fb-4bc0-90c2-5ecd32cb779b", "table_name": "ecommerce.orders", "schema_id": "ecommerce", "description": "This table contains the following fields: order_id, customer_id, order_date, total_amount, status, shipping_address, payment_id. The 'orders' table is used to store information about customer orders placed in the system, including details about the customer, the order date, the total amount of the order, its current status, the shipping address, and the associated payment information.", "field_count": 7, "fields": [{"name": "order_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each order."}, {"name": "customer_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The unique identifier for the customer who placed the order."}, {"name": "order_date", "data_type": "timestamp", "is_categorical": false, "is_datetime": true, "description": "The date when the order was placed."}, {"name": "total_amount", "data_type": "decimal", "is_categorical": false, "is_datetime": false, "description": "The total monetary amount for the order."}, {"name": "status", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": true, "is_datetime": false, "description": "The current status of the order (e.g., pending, shipped, completed, canceled).", "categorical_values": ["cancelled", "delivered", "pending", "processing", "shipped"]}, {"name": "shipping_address", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "The address where the order will be shipped."}, {"name": "payment_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The unique identifier for the payment transaction associated with the order."}], "sample_rows": "{\n  \"order_id\": 2,\n  \"customer_id\": 512,\n  \"order_date\": \"2025-02-15T12:45:10\",\n  \"total_amount\": 289.34,\n  \"status\": \"processing\",\n  \"shipping_address\": \"4821 Maple Avenue\\nNorth Newtown, CA 98765\",\n  \"payment_id\": null\n}"}, {"table_id": "aeffa7f7-7e3c-4790-a221-c7201052f3be", "table_name": "ecommerce.customers", "schema_id": "ecommerce", "description": "This table contains the following fields: customer_id, first_name, last_name, email, phone, address, created_at. The 'customers' table is designed to store information about individuals who have engaged with the business, allowing for effective customer management and communication.", "field_count": 7, "fields": [{"name": "customer_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each customer, typically an integer or UUID."}, {"name": "first_name", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The first name of the customer."}, {"name": "last_name", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The last name of the customer."}, {"name": "email", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The email address of the customer, used for communication and account verification."}, {"name": "phone", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The contact phone number of the customer."}, {"name": "address", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "The physical address of the customer, which may include street, city, state, and zip code."}, {"name": "created_at", "data_type": "timestamp", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the customer record was created."}], "sample_rows": "{\n  \"customer_id\": 2,\n  \"first_name\": \"<PERSON>\",\n  \"last_name\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"phone\": \"+************\",\n  \"address\": \"123 Random St Apt. 456\\nSampletown, ST 12345\",\n  \"created_at\": \"2025-06-15T12:34:56\"\n}"}, {"table_id": "98eaca72-f9ea-423a-a115-959cde8d3764", "table_name": "ecommerce.reviews", "schema_id": "ecommerce", "description": "This table contains the following fields: review_id, product_id, customer_id, rating, comment, created_at. The 'reviews' table is designed to store customer feedback on products. It captures essential information about each review, including the reviewer, the product being reviewed, the rating given, and any comments made by the customer, along with the timestamp of when the review was created.", "field_count": 6, "fields": [{"name": "review_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each review."}, {"name": "product_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The identifier for the product being reviewed."}, {"name": "customer_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The identifier for the customer who wrote the review."}, {"name": "rating", "data_type": "int", "is_categorical": true, "is_datetime": false, "description": "The rating given by the customer, typically on a scale (e.g., 1 to 5 stars).", "categorical_values": ["1", "2", "3", "4", "5"]}, {"name": "comment", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "The textual feedback provided by the customer regarding the product."}, {"name": "created_at", "data_type": "timestamp", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the review was created."}], "sample_rows": "{\n  \"review_id\": 2,\n  \"product_id\": 912,\n  \"customer_id\": 738,\n  \"rating\": 4,\n  \"comment\": \"Local between industry expert create. Feedback operate over fifty team find evaluate many. Essential low junior both.\",\n  \"created_at\": \"2025-03-15T10:45:12\"\n}"}, {"table_id": "260df639-fcf1-48a7-8b47-d0ed135a7b2f", "table_name": "finance.settings", "schema_id": "finance", "description": "This table contains the following fields: id, user_id, key, value. The 'settings' table is used to store configuration settings for users, allowing for personalized application behavior and preferences.", "field_count": 4, "fields": [{"name": "id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each setting record."}, {"name": "user_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The identifier for the user to whom the setting belongs."}, {"name": "key", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The name of the setting, which acts as a reference for the stored value."}, {"name": "value", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "The value associated with the key, representing the user's specific setting."}], "sample_rows": []}, {"table_id": "4954a8f4-ed4e-4fde-9ba2-508c257db46f", "table_name": "finance.messaging_connections", "schema_id": "finance", "description": "This table contains the following fields: id, user_id, type, config, db_connection_id. The 'messaging_connections' table is designed to store information about various messaging connections established by users. It tracks the type of connection, configuration details, and associates each connection with a specific user and database connection.", "field_count": 5, "fields": [{"name": "id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each messaging connection."}, {"name": "user_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The identifier for the user who owns the messaging connection."}, {"name": "type", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The type of messaging connection (e.g., email, SMS, chat)."}, {"name": "config", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "Configuration settings related to the messaging connection, stored in a structured format."}, {"name": "db_connection_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The identifier for the database connection associated with this messaging connection."}], "sample_rows": []}, {"table_id": "a9a58039-e039-460c-b53e-c39c87859223", "table_name": "finance.otps", "schema_id": "finance", "description": "This table contains the following fields: id, email, otp_code, expires_at, is_used, created_at. The 'otps' table is used to store one-time password (OTP) information for user authentication purposes. It tracks the OTPs generated for users, their expiration times, and whether they have been used or not.", "field_count": 6, "fields": [{"name": "id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each OTP record."}, {"name": "email", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The email address of the user associated with the OTP."}, {"name": "otp_code", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The one-time password generated for the user."}, {"name": "expires_at", "data_type": "datetime", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the OTP will expire."}, {"name": "is_used", "data_type": "tinyint", "is_categorical": true, "is_datetime": false, "description": "A boolean flag indicating whether the OTP has been used.", "categorical_values": ["1"]}, {"name": "created_at", "data_type": "datetime", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the OTP record was created."}], "sample_rows": "{\n  \"id\": 2,\n  \"email\": \"<EMAIL>\",\n  \"otp_code\": \"543210\",\n  \"expires_at\": \"2026-07-31T12:45:00\",\n  \"is_used\": 0,\n  \"created_at\": \"2026-07-31T12:35:00\"\n}"}, {"table_id": "e97bc10a-9c56-4a2c-8f14-024d2057bf1d", "table_name": "finance.pending_registrations", "schema_id": "finance", "description": "This table contains the following fields: id, email, full_name, hashed_password, organization_name, organization_description, created_at, is_verified. The purpose of this table is to store information about users who have registered but have not yet completed the verification process. It holds the necessary details to manage and verify their registrations before granting them full access to the system.", "field_count": 8, "fields": [{"name": "id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each registration entry."}, {"name": "email", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The email address of the user registering."}, {"name": "full_name", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The full name of the user registering."}, {"name": "hashed_password", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The hashed version of the user's password for secure storage."}, {"name": "organization_name", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The name of the organization the user is associated with."}, {"name": "organization_description", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "A brief description of the organization."}, {"name": "created_at", "data_type": "datetime", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the registration was created."}, {"name": "is_verified", "data_type": "tinyint", "is_categorical": true, "is_datetime": false, "description": "A boolean flag indicating whether the user's registration has been verified.", "categorical_values": ["1"]}], "sample_rows": "{\n  \"id\": 2,\n  \"email\": \"<EMAIL>\",\n  \"full_name\": \"john doe\",\n  \"hashed_password\": \"$2b$12$randomhashedpassword1234567890abcdefg\",\n  \"organization_name\": \"exampleorg\",\n  \"organization_description\": null,\n  \"created_at\": \"2025-07-01T12:00:00\",\n  \"is_verified\": 0\n}"}, {"table_id": "08bdc385-a8c3-47c7-80a6-c864a3237c26", "table_name": "finance.users", "schema_id": "finance", "description": "This table contains the following fields: id, full_name, organization_name, email, hashed_password, organization_description, role. The 'users' table is designed to store information about individuals who have registered within the system, including their personal details, organizational affiliations, and access credentials.", "field_count": 7, "fields": [{"name": "id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each user in the table."}, {"name": "full_name", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The full name of the user."}, {"name": "organization_name", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The name of the organization the user is affiliated with."}, {"name": "email", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The email address of the user, used for communication and login purposes."}, {"name": "hashed_password", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The hashed version of the user's password for secure authentication."}, {"name": "organization_description", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "A brief description of the user's organization."}, {"name": "role", "data_type": "enum", "is_categorical": true, "is_datetime": false, "description": "The role of the user within the system, indicating their level of access and permissions.", "categorical_values": ["member"]}], "sample_rows": "{\n  \"id\": 2,\n  \"full_name\": \"jane doe\",\n  \"organization_name\": \"examplecorp\",\n  \"email\": \"<EMAIL>\",\n  \"hashed_password\": \"$2b$12$randomhashedpassword1234567890abcdefg\",\n  \"organization_description\": null,\n  \"role\": \"admin\"\n}"}, {"table_id": "ce810baf-986b-466c-aaf0-207b41399371", "table_name": "finance.connection_tables", "schema_id": "finance", "description": "This table contains the following fields: id, connection_id, schema_name, name, description, status, sample_row. The 'connection_tables' table is designed to store metadata about database tables that are associated with specific connections. It provides essential information about each table, including its schema and status, which is crucial for managing and utilizing database connections effectively.", "field_count": 7, "fields": [{"name": "id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each record in the table."}, {"name": "connection_id", "data_type": "int", "is_categorical": true, "is_datetime": false, "description": "The identifier for the connection associated with the table.", "categorical_values": ["2", "3", "4", "5"]}, {"name": "schema_name", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": true, "is_datetime": false, "description": "The name of the schema to which the table belongs.", "categorical_values": ["ecommerce", "finance", "TRANSFORMED"]}, {"name": "name", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The name of the table."}, {"name": "description", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "A brief description of the table's purpose or content."}, {"name": "status", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": true, "is_datetime": false, "description": "The current status of the table (e.g., active, inactive).", "categorical_values": ["inactive"]}, {"name": "sample_row", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "An example row of data from the table, used for reference."}], "sample_rows": "{\n  \"id\": 27,\n  \"connection_id\": 5,\n  \"schema_name\": \"investment\",\n  \"name\": \"investment.portfolio_performance\",\n  \"description\": \"This table contains the following fields: performance_id, portfolio_id, yield, performance_date. The purpose of the 'portfolio_performance' table is to track the performance metrics generated from various portfolios over time, allowing for analysis of portfolio effectiveness and financial reporting.\",\n  \"status\": \"active\",\n  \"sample_row\": \"[]\"\n}"}, {"table_id": "52e83d1e-0450-4888-97bd-1d3a0ad5511a", "table_name": "finance.fields", "schema_id": "finance", "description": "This table contains the following fields: id, connection_table_id, name, data_type, description, is_categorical, is_datetime, categorical_values, status. The 'fields' table is designed to store metadata about various fields in a database, including their types, descriptions, and characteristics, which helps in understanding and managing the data structure effectively.", "field_count": 9, "fields": [{"name": "id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each field in the table."}, {"name": "connection_table_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The identifier for the table that this field is associated with."}, {"name": "name", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The name of the field, representing the data it holds."}, {"name": "data_type", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": true, "is_datetime": false, "description": "The type of data stored in the field (e.g., integer, string, date).", "categorical_values": ["ARRAY", "boolean", "character varying", "date", "integer", "NUMBER", "numeric", "text", "timestamp without time zone", "TIMESTAMP_NTZ", "VARIANT"]}, {"name": "description", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "A brief description of the field's purpose or content."}, {"name": "is_categorical", "data_type": "tinyint", "is_categorical": true, "is_datetime": false, "description": "A boolean indicating whether the field is categorical (true) or not (false).", "categorical_values": ["0", "1"]}, {"name": "is_datetime", "data_type": "tinyint", "is_categorical": true, "is_datetime": false, "description": "A boolean indicating whether the field stores date and time values (true) or not (false).", "categorical_values": ["0", "1"]}, {"name": "categorical_values", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "A list of possible values for the field if it is categorical."}, {"name": "status", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": true, "is_datetime": false, "description": "The current status of the field, indicating whether it is active, inactive, or deprecated.", "categorical_values": ["active"]}], "sample_rows": "{\n  \"id\": 742,\n  \"connection_table_id\": 58,\n  \"name\": \"return_id\",\n  \"data_type\": \"integer\",\n  \"description\": \"A unique identifier for each return record.\",\n  \"is_categorical\": 0,\n  \"is_datetime\": 0,\n  \"categorical_values\": null,\n  \"status\": \"inactive\"\n}"}, {"table_id": "e7609fb0-2157-4b67-a7b4-c51fefe87cad", "table_name": "finance.connections", "schema_id": "finance", "description": "This table contains the following fields: id, user_id, name, database_type, database_platform, connection_params, created_at, updated_at, status. The 'connections' table is designed to store information about various database connections established by users. It tracks the details necessary for connecting to different databases, including user-specific configurations and statuses.", "field_count": 9, "fields": [{"name": "id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each connection record."}, {"name": "user_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The identifier of the user who owns the connection."}, {"name": "name", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "A user-defined name for the connection."}, {"name": "database_type", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": true, "is_datetime": false, "description": "The type of database (e.g., SQL, NoSQL) that the connection is for.", "categorical_values": ["sql"]}, {"name": "database_platform", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": true, "is_datetime": false, "description": "The specific database platform (e.g., MySQL, PostgreSQL) being used.", "categorical_values": ["postgres", "snowflake"]}, {"name": "connection_params", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "A JSON object containing parameters required to establish the connection."}, {"name": "created_at", "data_type": "datetime", "is_categorical": false, "is_datetime": true, "description": "The timestamp when the connection record was created."}, {"name": "updated_at", "data_type": "datetime", "is_categorical": false, "is_datetime": true, "description": "The timestamp when the connection record was last updated."}, {"name": "status", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": true, "is_datetime": false, "description": "The current status of the connection (e.g., active, inactive).", "categorical_values": ["connected"]}], "sample_rows": "{\n  \"id\": 5,\n  \"user_id\": 3,\n  \"name\": \"mysql_example\",\n  \"database_type\": \"sql\",\n  \"database_platform\": \"mysql\",\n  \"connection_params\": \"{\\\"host\\\": \\\"database-2.abcd1234efgh.us-west-2.rds.amazonaws.com\\\", \\\"port\\\": 3306, \\\"database\\\": \\\"test_database\\\", \\\"user\\\": \\\"example_user\\\", \\\"password\\\": \\\"example-Pass45&7\\\", \\\"schema\\\": null}\",\n  \"created_at\": \"2026-01-15T12:30:00\",\n  \"updated_at\": \"2026-01-15T12:30:00\",\n  \"status\": \"disconnected\"\n}"}]}