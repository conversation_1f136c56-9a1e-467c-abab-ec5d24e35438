# Key fixes for preventing duplicate message processing

import asyncio
import time
import threading
from datetime import datetime, timed<PERSON><PERSON>
from typing import Set, Dict, Optional
# Standard library imports
import asyncio
import base64
import glob
import json
import logging
import os
import re
import tempfile
import threading
import time
import traceback
import uuid
from collections import defaultdict
from datetime import datetime, timedelta
from pathlib import Path
from typing import Optional, Dict, Any, List

# Third-party imports
import pandas as pd
import requests
from dotenv import load_dotenv
from fastapi import (
    APIRouter,
    BackgroundTasks,
    Depends,
    FastAPI,
    HTTPException,
    Query,
    Request,
    Response
)
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse, HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from pydantic import BaseModel, Field
from slack_sdk import WebClient
from tabulate import tabulate
from app.core.redis import get_redis  # Import get_redis from the new module

# Local application imports
from scripts.plot import generate_plot
from sql_copilot.handlers.feedback_manager import save_feedback_and_execute_sql
from sql_copilot.handlers.slack_messages import fetch_user_questions
from sql_copilot.handlers.slack_message_tracker import (
    create_slack_message_entry,
    update_slack_message_status,
    get_slack_message_by_id,
    delete_slack_message_by_id
)
from sql_copilot.services.ai.query import QueryHandler
from config import Config
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Constants
current_dir = os.path.dirname(os.path.abspath(__file__))



# Directory to store feedback data
FEEDBACK_DIR = Path("./temp_feedback")

# Ensure feedback directory exists
os.makedirs(FEEDBACK_DIR, exist_ok=True)


# Initialize Redis connection


router = APIRouter(
    prefix="/integrations",
    tags=["third party apps integrations API"])


SLACK_BOT_TOKEN = Config.SLACK_BOT_TOKEN
SQL_COPILOT_URL = f"{Config.BASE_URL}/query"
SQL_COPILOT_DOWNLOAD_URL = f"{Config.BASE_URL}/files/download"
SQL_COPILOT_DOWNLOAD_URL_PLOT = f"{Config.BASE_URL}/download-plots"
client = WebClient(token=SLACK_BOT_TOKEN)


LOADING_MESSAGES = [
    {
        "type": "section",
        "text": {
            "type": "mrkdwn",
            "text": "🤔 Thinking..."
        }
    }
]


# Get bot's own ID once at startup
try:
    bot_info = client.auth_test()
    BOT_USER_ID = bot_info["user_id"]
    BOT_APP_ID = bot_info.get("app_id")
    
except Exception as e:
    print(f"Error getting bot ID: {e}")
    BOT_USER_ID = None
    BOT_APP_ID = None
# Enhanced deduplication mechanisms



def send_loading_message(channel, thread_ts=None):
    """Send a loading message and return its timestamp"""
    try:
        response = client.chat_postMessage(
            channel=channel,
            thread_ts=thread_ts,
            blocks=LOADING_MESSAGES,
            text="Processing your request..."  # Fallback text
        )
        return response["ts"]
    except Exception as e:
        print(f"Error sending loading message: {e}")
        return None
    
def update_message_with_response(channel, loading_message_ts, blocks, text):
    """Update the loading message with the actual response"""
    try:
        client.chat_update(
            channel=channel,
            ts=loading_message_ts,
            blocks=blocks,
            text=text
        )
    except Exception as e:
        print(f"Error updating message: {e}")


def extract_question_from_message(text, bot_user_id):
    """Extract the actual question from a message that might contain a bot mention"""
    text = re.sub(f'<@{bot_user_id}>', '', text)
    return text.strip()



from slack_sdk.errors import SlackApiError

def upload_file(file_path, title, channel, thread_ts, initial_comment=None):
    try:
        # Upload the file to Slack
        response = client.files_upload_v2(
            file=file_path,
            title=title,
            channel=channel,
            thread_ts=thread_ts,
            initial_comment=initial_comment
        )
        print("File uploaded successfully:", response['file']['url_private'])
        if os.path.exists(file_path):
            os.remove(file_path)
        return response
    except SlackApiError as e:
        print(f"Error uploading file: {e.response['error']}")
        return None


class RedisMessageProcessor:
    def __init__(self):
        self.redis = None
        self.processing_key = "slack:processing_messages"
        self.processed_key = "slack:processed_messages"
        self.timestamps_key = "slack:message_timestamps"
        self._cleanup_interval = 300  # 5 minutes
        self._last_cleanup = time.time()
        self._init_redis()
        
    def _init_redis(self):
        """Initialize Redis client with retry logic"""
        max_retries = 3
        retry_delay = 1  # seconds
        
        for attempt in range(max_retries):
            try:
                self.redis = get_redis()
                if self.redis is not None:
                    logger.info("Successfully initialized Redis client")
                    return True
                logger.warning(f"Redis client not initialized, attempt {attempt + 1}/{max_retries}")
                time.sleep(retry_delay)
            except Exception as e:
                logger.error(f"Error initializing Redis client: {e}")
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
        
        logger.error("Failed to initialize Redis client after all retries")
        return False
        
    async def _cleanup_old_entries(self):
        """Remove old entries to prevent memory leaks"""
        if not self.redis:
            self._init_redis()  # Try to reinitialize Redis
            if not self.redis:
                logger.error("Redis client not initialized")
                return
            
        current_time = time.time()
        if current_time - self._last_cleanup < self._cleanup_interval:
            return
            
        # Remove entries older than 1 hour
        cutoff_time = current_time - 3600
        
        try:
            # Get all timestamps
            timestamps = await self.redis.hgetall(self.timestamps_key)
            old_keys = [k for k, v in timestamps.items() if float(v) < cutoff_time]
            
            if old_keys:
                # Remove from all sets
                await self.redis.srem(self.processing_key, *old_keys)
                await self.redis.srem(self.processed_key, *old_keys)
                await self.redis.hdel(self.timestamps_key, *old_keys)
                
            self._last_cleanup = current_time
        except Exception as e:
            logger.error(f"Error cleaning up old entries: {e}")
            self.redis = None  # Reset Redis client on error
    
    async def is_processing_or_processed(self, message_id: str) -> tuple[bool, str]:
        """Check if message is being processed or already processed"""
        if not self.redis:
            self._init_redis()  # Try to reinitialize Redis
            if not self.redis:
                logger.error("Redis client not initialized")
                return False, "redis_not_initialized"
            
        await self._cleanup_old_entries()
        
        try:
            # Use Redis pipeline for atomic operations
            pipe = self.redis.pipeline()
            pipe.sismember(self.processing_key, message_id)
            pipe.sismember(self.processed_key, message_id)
            is_processing, is_processed = await pipe.execute()
            
            if is_processing:
                return True, "processing"
            if is_processed:
                return True, "processed"
            return False, "new"
        except Exception as e:
            logger.error(f"Error checking message status: {e}")
            self.redis = None  # Reset Redis client on error
            return False, "error"
    
    async def mark_processing(self, message_id: str) -> bool:
        """Mark message as processing. Returns False if already processing/processed"""
        if not self.redis:
            self._init_redis()  # Try to reinitialize Redis
            if not self.redis:
                logger.error("Redis client not initialized")
                return False
            
        try:
            # Use Redis transaction for atomicity
            pipe = self.redis.pipeline()
            await pipe.watch(self.processing_key, self.processed_key)
            
            # Check if already processing or processed
            is_processing = await self.redis.sismember(self.processing_key, message_id)
            is_processed = await self.redis.sismember(self.processed_key, message_id)
            
            if is_processing or is_processed:
                return False
            
            # Add to processing set and update timestamp
            pipe.multi()
            pipe.sadd(self.processing_key, message_id)
            pipe.hset(self.timestamps_key, message_id, time.time())
            await pipe.execute()
            return True
            
        except Exception as e:
            logger.error(f"Error marking message as processing: {e}")
            self.redis = None  # Reset Redis client on error
            return False
    
    async def mark_completed(self, message_id: str):
        """Mark message as completed"""
        if not self.redis:
            self._init_redis()  # Try to reinitialize Redis
            if not self.redis:
                logger.error("Redis client not initialized")
                return
            
        try:
            pipe = self.redis.pipeline()
            pipe.srem(self.processing_key, message_id)
            pipe.sadd(self.processed_key, message_id)
            pipe.hset(self.timestamps_key, message_id, time.time())
            await pipe.execute()
        except Exception as e:
            logger.error(f"Error marking message as completed: {e}")
            self.redis = None  # Reset Redis client on error
    
    async def mark_failed(self, message_id: str):
        """Mark message as failed (remove from processing)"""
        if not self.redis:
            self._init_redis()  # Try to reinitialize Redis
            if not self.redis:
                logger.error("Redis client not initialized")
                return
            
        try:
            pipe = self.redis.pipeline()
            pipe.srem(self.processing_key, message_id)
            pipe.hset(self.timestamps_key, message_id, time.time())
            await pipe.execute()
        except Exception as e:
            logger.error(f"Error marking message as failed: {e}")
            self.redis = None  # Reset Redis client on error

# Global processor instance
message_processor = RedisMessageProcessor()

def create_composite_message_id(event: dict) -> str:
    """Create a composite ID that's more reliable than client_msg_id alone"""
    client_msg_id = event.get("client_msg_id", "")
    channel = event.get("channel", "")
    ts = event.get("ts", "")
    user = event.get("user", "")
    text_hash = str(hash(event.get("text", "")))[:8]  # Short hash of text
    
    # Use multiple identifiers to create a unique composite ID
    return f"{client_msg_id}_{channel}_{ts}_{user}_{text_hash}"

def should_process_message(event: dict) -> tuple[bool, str]:
    """Enhanced message filtering with detailed reasons"""
    # Basic bot filtering
    if any([
        "bot_id" in event,
        "bot_profile" in event,
        event.get("user") == BOT_USER_ID,
        event.get("subtype") == "bot_message",
        event.get("subtype") == "message_changed",
        event.get("subtype") == "message_deleted"
    ]):
        return False, "bot_message"
    
    # Check for required fields
    if not event.get("user"):
        return False, "no_user"
    
    if not event.get("text", "").strip():
        return False, "empty_text"
    
    # Check if it's a direct message or mentions the bot
    text = event.get("text", "")
    is_dm = event.get("channel_type") == "im"
    is_mention = f"<@{BOT_USER_ID}>" in text
    
    if not (is_dm or is_mention):
        return False, "not_addressed_to_bot"
    
    return True, "should_process"


async def query_sql_copilot(question,conversation_history=None):
    """Send question to SQL Copilot API and get response"""
    try:
        from config import test_params
        #from test_keys import connection_params_mysql,connection_params_postgres
        #provider = "postgres"
        provider = "snowflake"
        connection_id = "test"
        #connection_params = test_params
        db_type = "sql"
        handler = QueryHandler(provider=provider,connection_params=test_params,query=question,db_type=db_type,connection_id=connection_id,history=conversation_history)
        return await handler.process_query()
    except Exception as e:
        print(f"Error querying SQL Copilot: {e}")
        return None
    

def create_slack_blocks(api_response, thread_ts, channel):
    blocks = []
    
    # Add question interpretation if present (as a context block)
    question_interpretation = api_response.get('question_interpretation')
    if question_interpretation and question_interpretation.strip():
        blocks.append({
            "type": "context",
            "elements": [
                {
                    "type": "mrkdwn",
                    "text": f"_I understood your question as:_ {question_interpretation}"
                }
            ]
        })
    
    # 1. Add response text (always included if present)
    response_text = api_response.get('response', '')
    if response_text.strip():  # Only add if there's actual content
        blocks.append({
            "type": "section",
            "text": {
                "type": "mrkdwn",
                "text": response_text
            }
        })
        
        # Add a divider if we have more content coming
        if any([
            api_response.get('query_code'),
            api_response.get('code_data'),
            api_response.get('has_file')
        ]):
            blocks.append({"type": "divider"})

    # 2. Add SQL query if present
    sql_query = api_response.get('query_code')
    if sql_query:
        blocks.append({
            "type": "section",
            "text": {
                "type": "mrkdwn",
                "text": "*SQL Query:*\n```sql\n" + sql_query.strip() + "\n```"
            }
        })
        
        blocks.append({"type": "divider"})

    # 3. Add table data (only in analytics mode)
    if api_response.get('analytics_mode'):
        code_data = api_response.get('code_data', [])
        if code_data:
            try:
                code_df = pd.DataFrame(code_data)
                
                # Save dataframe to a temporary CSV file
                with tempfile.NamedTemporaryFile(delete=False, suffix=".csv") as temp_file:
                    file_path = temp_file.name
                    code_df.to_csv(file_path, index=False)

                # Only attempt upload if file exists
                if os.path.exists(file_path):
                    upload_response = upload_file(
                        file_path=file_path,
                        title="Data",
                        channel=channel,
                        thread_ts=thread_ts,
                        initial_comment="Data"
                    )
                    if not upload_response:
                        raise Exception("File upload failed")
                    os.remove(file_path)

                else:
                    raise FileNotFoundError(f"File not found: {file_path}")

            except Exception as table_error:
                logger.error(f"Error formatting table: {table_error}")
                pass

    # 4. Handle file uploads/downloads
    if api_response.get('has_file') and api_response.get('file_name'):
        if api_response.get("analytics_mode"):
            # Upload file for analytics mode
            try:
                file_name = api_response['file_name']
                file_path = os.path.abspath(os.path.join("static", "plots", file_name))
                
                # Only attempt upload if file exists
                if os.path.exists(file_path):
                    upload_response = upload_file(
                        file_path=file_path,
                        title="Generated Graph",
                        channel=channel,
                        thread_ts=thread_ts,
                        initial_comment="📊 Generated visualization"
                    )
                    if not upload_response:
                        raise Exception("File upload failed")
                else:
                    raise FileNotFoundError(f"File not found: {file_path}")
                    
            except Exception as upload_error:
                logger.error(f"Error uploading file: {upload_error}")
                blocks.append({
                    "type": "section",
                    "text": {
                        "type": "mrkdwn",
                        "text": "❌ *Error uploading the visualization. Please try again.*"
                    }
                })
        else:
            # Add download button for non-analytics mode
            file_name = api_response['file_name']
            download_url = f"{SQL_COPILOT_DOWNLOAD_URL}/{file_name}"
            
            blocks.append({
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": "📥 *Download the generated file:*"
                }
            })
            
            blocks.append({
                "type": "actions",
                "elements": [{
                    "type": "button",
                    "text": {
                        "type": "plain_text",
                        "text": "Download File",
                        "emoji": True
                    },
                    "url": download_url,
                    "action_id": "download_file"
                }]
            })
    if api_response.get('query_code'):
        # feedback section at the end
        blocks.append({"type": "divider"})
        
        blocks.append({
            "type": "section",
            "block_id":"added_feedback_message",
            "text": {
                "type": "mrkdwn",
                "text": "Was this response helful? Please provide feedback, Your feedback helps us better increase the accuracy of the the bot"
            }
        })
        
        blocks.append({
            "type": "actions",
            "block_id": "feedback_buttons",
            "elements": [
                {
                    "type": "button",
                    "text": {
                        "type": "plain_text",
                        "text": "👍 Yes",
                        "emoji": True
                    },
                    "style": "primary",
                    "value": "yes",
                    "action_id": "feedback_yes"
                },
                {
                    "type": "button",
                    "text": {
                        "type": "plain_text",
                        "text": "👎 No",
                        "emoji": True
                    },
                    "style": "danger",
                    "value": "no",
                    "action_id": "feedback_no"
                }
            ]
        })

    return blocks
 
async def enhanced_slack_events(request: Request):
    """Enhanced event handler with better duplicate prevention"""
    try:
        data = await request.json()
    except Exception as e:
        logger.error(f"Error parsing JSON: {e}")
        raise HTTPException(status_code=400, detail="Invalid JSON")

    # Slack URL Verification Challenge
    if "challenge" in data:
        return {"challenge": data["challenge"]}

    if "event" not in data:
        raise HTTPException(status_code=400, detail="No event in request")

    event = data["event"]
    
    # Log the event for debugging
    logger.info(f"Received event DATA: {event}")

    # Enhanced message filtering
    should_process, reason = should_process_message(event)
    if not should_process:
        logger.debug(f"Skipping message - {reason}")
        return {"status": "ignored", "reason": reason}

    # Extract essential information
    channel = event.get("channel")
    message_ts = event.get("ts")
    thread_ts = event.get("thread_ts") or message_ts
    user_id = event.get("user")
    text = event.get("text", "")

    # Create composite message ID for better uniqueness
    composite_id = create_composite_message_id(event)
    
    # Check if we're already processing or have processed this message

    logger.info(f"Checking for duplicate message: {composite_id}")
    is_duplicate, status = await message_processor.is_processing_or_processed(composite_id)
    logger.info(f"Is duplicate: {is_duplicate}, Status: {status}")

    if is_duplicate:
        logger.info(f"Duplicate message detected: {composite_id} (status: {status})")
        return {"status": "ignored", "reason": f"duplicate_{status}"}

    # Try to mark as processing (atomic operation)
    if not await message_processor.mark_processing(composite_id):
        logger.info(f"Failed to mark message as processing: {composite_id}")
        return {"status": "ignored", "reason": "already_processing"}

    logger.info(f"Processing message: {composite_id}")
    
    # Database-level deduplication as backup
    client_message_id = event.get("client_msg_id", composite_id)
    try:
        # Check database first - only if Redis check passed
        message_exists, error = await get_slack_message_by_id(client_message_id)
        if message_exists and message_exists.get("status") == "responded":
            logger.info(f"Message already responded in database: {client_message_id}")
            await message_processor.mark_completed(composite_id)
            return {"status": "ignored", "reason": "already_responded_db"}

        # Create database entry only if it doesn't exist
        if not message_exists:
            success, error = await create_slack_message_entry(
                thread_id=thread_ts,
                channel_id=channel,
                message_id=client_message_id,
                status="processing"
            )
            if not success:
                logger.error(f"Failed to create database entry: {error}")
                # Don't fail the request, just log the error
                # The Redis-based deduplication will still work

    except Exception as e:
        logger.error(f"Database operation error: {e}")
        # Don't fail the request, just log the error
        # The Redis-based deduplication will still work

    # Process the message
    loading_message_ts = None
    try:
        # Send loading message
        loading_message_ts = send_loading_message(channel, thread_ts)
        
        # Extract and process question
        question = extract_question_from_message(text, BOT_USER_ID)
        
        # Get conversation history
        conversation_thread = fetch_user_questions(channel=channel, ts=thread_ts)["message_history"]
        
        # Query SQL Copilot
        api_response = await query_sql_copilot(question, conversation_thread)
        if not api_response:
            raise Exception("Failed to get response from SQL Copilot")
        
        api_response = api_response.dict()

        print(f"QUERY HANDLER RESPONSE: {api_response}")
        
        # Create and send response blocks
        blocks = create_slack_blocks(api_response, thread_ts, channel)
        
        if loading_message_ts:
            update_message_with_response(
                channel=channel,
                loading_message_ts=loading_message_ts,
                blocks=blocks,
                text=api_response.get('response', 'Query response')
            )
        else:
            client.chat_postMessage(
                channel=channel,
                thread_ts=thread_ts,
                blocks=blocks,
                text=api_response.get('response', 'Query response')
            )

        # Update database status to responded - do this asynchronously
        try:
            await update_slack_message_status(client_message_id, "responded")
        except Exception as db_error:
            logger.error(f"Failed to update database status to responded: {db_error}")
            # Don't fail the request, just log the error

        # Mark as completed in Redis
        await message_processor.mark_completed(composite_id)
        
        logger.info(f"Successfully processed message: {composite_id}")
        return {"status": "success"}

    except Exception as e:
        logger.error(f"Error processing message {composite_id}: {e}")
        
        # Clean up loading message if it exists
        if loading_message_ts:
            try:
                client.chat_delete(channel=channel, ts=loading_message_ts)
            except:
                pass
        
        # Send error message to user
        try:
            client.chat_postMessage(
                channel=channel,
                thread_ts=thread_ts,
                text="Sorry, I encountered an error processing your request. Please try again later."
            )
        except Exception as send_error:
            logger.error(f"Failed to send error message: {send_error}")
        
        # Mark as failed in Redis
        await message_processor.mark_failed(composite_id)
        
        # Update database status to failed - do this asynchronously
        try:
            await update_slack_message_status(client_message_id, "failed")
        except Exception as db_error:
            logger.error(f"Failed to update database status to failed: {db_error}")
            # Don't fail the request, just log the error
        
        raise HTTPException(status_code=500, detail=str(e))

# Integration with your existing router
@router.post("/slack/events")
async def slack_events(request: Request):
    """Replace your existing endpoint with this enhanced version"""
    return await enhanced_slack_events(request)

# Additional enhancements to your existing functions:

def enhanced_send_loading_message(channel, thread_ts=None):
    """Enhanced loading message with retry logic"""
    max_retries = 3
    for attempt in range(max_retries):
        try:
            response = client.chat_postMessage(
                channel=channel,
                thread_ts=thread_ts,
                blocks=LOADING_MESSAGES,
                text="Processing your request..."
            )
            return response["ts"]
        except Exception as e:
            logger.warning(f"Failed to send loading message (attempt {attempt + 1}): {e}")
            if attempt == max_retries - 1:
                logger.error(f"Failed to send loading message after {max_retries} attempts")
                return None
            time.sleep(0.5)  # Brief delay before retry
    return None


def enhanced_update_message_with_response(channel, loading_message_ts, blocks, text):
    """Enhanced message update with retry logic"""
    max_retries = 3
    for attempt in range(max_retries):
        try:
            client.chat_update(
                channel=channel,
                ts=loading_message_ts,
                blocks=blocks,
                text=text
            )
            return True
        except Exception as e:
            logger.warning(f"Failed to update message (attempt {attempt + 1}): {e}")
            if attempt == max_retries - 1:
                logger.error(f"Failed to update message after {max_retries} attempts")
                return False
            time.sleep(0.5)
    return False

# Database schema suggestions for better deduplication:
"""
-- Add these to your database schema:

-- Add index for faster lookups
CREATE INDEX IF NOT EXISTS idx_slack_messages_message_id ON slack_messages(message_id);
CREATE INDEX IF NOT EXISTS idx_slack_messages_status ON slack_messages(status);
CREATE INDEX IF NOT EXISTS idx_slack_messages_created_at ON slack_messages(created_at);

-- Add constraint to prevent duplicates (handle gracefully in code)
-- ALTER TABLE slack_messages ADD CONSTRAINT unique_message_id UNIQUE(message_id);

-- Add timestamps if they don't exist
ALTER TABLE slack_messages ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE slack_messages ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

-- Add composite index for better performance
CREATE INDEX IF NOT EXISTS idx_slack_messages_channel_thread 
ON slack_messages(channel_id, thread_id, created_at);
"""

# Rate limiting integration
class RateLimiter:
    def __init__(self, max_calls: int = 10, time_window: int = 60):
        self.max_calls = max_calls
        self.time_window = time_window
        self.calls = {}
        self.lock = threading.Lock()
    
    def is_allowed(self, key: str) -> bool:
        with self.lock:
            now = time.time()
            
            # Clean old entries
            if key in self.calls:
                self.calls[key] = [call_time for call_time in self.calls[key] 
                                  if now - call_time < self.time_window]
            else:
                self.calls[key] = []
            
            # Check if under limit
            if len(self.calls[key]) < self.max_calls:
                self.calls[key].append(now)
                return True
            
            return False


# Helper functions for JSON file storage
async def store_feedback_data(feedback_id, feedback_data):
    """Store feedback data in a JSON file"""
    try:
        file_path = FEEDBACK_DIR / f"{feedback_id}.json"
        with open(file_path, 'w') as f:
            json.dump(feedback_data, f)
        return True
    except Exception as e:
        logger.error(f"Error storing feedback data: {e}")
        return False

async def get_feedback_data(feedback_id):
    """Retrieve feedback data from a JSON file"""
    try:
        file_path = FEEDBACK_DIR / f"{feedback_id}.json"
        if not file_path.exists():
            return None
        with open(file_path, 'r') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"Error retrieving feedback data: {e}")
        return None
    


async def delete_all_feedback_data():
    """Delete all feedback data files"""
    try:
        files = glob.glob(str(FEEDBACK_DIR / "*.json"))  # Get all JSON files
        for file in files:
            os.remove(file)  # Delete each file
        return True
    except Exception as e:
        logger.error(f"Error deleting all feedback data: {e}")
        return False


@router.post("/slack/interactivity")
async def handle_slack_interactivity(request: Request):
    try:
        form_data = await request.form()
        payload = json.loads(form_data.get("payload", "{}"))
        
        # Handle different types of interactions
        interaction_type = payload.get("type", "")
        
        # Handle initial button clicks to open modal
        if interaction_type == "block_actions" and "actions" in payload and len(payload["actions"]) > 0:
            action_id = payload.get("actions", [{}])[0].get("action_id", "")
            
            if action_id.startswith("feedback_"):
                # Get relevant data
                channel = payload.get("channel", {}).get("id")
                message_ts = payload.get("message", {}).get("ts")
                thread_ts = payload.get("message", {}).get("thread_ts")
                user = payload.get("user", {}).get("id")
                trigger_id = payload.get("trigger_id", "")
                is_correct = action_id == "feedback_yes"
                
                # Extract SQL query from the message blocks
                message_blocks = payload.get("message", {}).get("blocks", [])
                sql_query = ""
                print(f"Messages block : {message_blocks}")
                # Find SQL query from the blocks
                for block in message_blocks:
                    if block.get("type") == "section" and "text" in block:
                        text_content = block.get("text", {}).get("text", "")
                        if "```sql" in text_content:
                            # Extract SQL between backticks
                            sql_start = text_content.find("```sql\n") + 7
                            sql_end = text_content.find("\n```", sql_start)
                            if sql_start > 6 and sql_end > sql_start:
                                sql_query = text_content[sql_start:sql_end].strip()
                                break
                
                # Generate a unique feedback ID
                timestamp = int(time.time())
                feedback_id = f"feedback_{timestamp}_{user}"
                
                # Store feedback data in a temporary JSON file
                feedback_data = {
                    "channel": channel,
                    "message_ts": message_ts,
                    "thread_ts": thread_ts,
                    "sql_query": sql_query,
                    "is_correct": is_correct,
                    "user": user,
                    "message_blocks": message_blocks,  # Store the full message blocks here
                    "created_at": timestamp
                }
                
                # Store in JSON file
                success = await store_feedback_data(feedback_id, feedback_data)
                if not success:
                    logger.error("Failed to store feedback data")
                    return {"status": "error", "detail": "Failed to store feedback data"}
                
                # Create modal with ONLY the feedback ID
                modal = {
                    "type": "modal",
                    "callback_id": "feedback_modal_submit",
                    "private_metadata": json.dumps({"feedback_id": feedback_id}),
                    "title": {
                        "type": "plain_text",
                        "text": "Additional Feedback"
                    },
                    "submit": {
                        "type": "plain_text",
                        "text": "Submit"
                    },
                    "close": {
                        "type": "plain_text",
                        "text": "Cancel"
                    },
                    "blocks": [
                        {
                            "type": "section",
                            "text": {
                                "type": "mrkdwn",
                                "text": f"You selected: *{'👍 Helpful' if is_correct else '👎 Not helpful'}*\n\nPlease provide any additional comments (optional):"
                            }
                        },
                        {
                            "type": "input",
                            "block_id": "feedback_comment",
                            "optional": True,
                            "element": {
                                "type": "plain_text_input",
                                "action_id": "comment",
                                "multiline": True,
                                "placeholder": {
                                    "type": "plain_text",
                                    "text": "What worked well or could be improved?"
                                }
                            },
                            "label": {
                                "type": "plain_text",
                                "text": "Comments"
                            }
                        }
                    ]
                }
                
                # Open the modal
                client.views_open(trigger_id=trigger_id, view=modal)
                return {"status": "success"}
                
        # Handle modal submissions
        elif interaction_type == "view_submission" and payload.get("view", {}).get("callback_id") == "feedback_modal_submit":
            view = payload.get("view", {})
            
            # Extract just the feedback ID
            metadata = json.loads(view.get("private_metadata", "{}"))
            feedback_id = metadata.get("feedback_id")
            
            if not feedback_id:
                return {"response_action": "errors", "errors": {"feedback_comment": "Missing feedback ID."}}
            
            # Retrieve the stored feedback data from JSON file
            feedback_data = await get_feedback_data(feedback_id)
            
            if not feedback_data:
                return {"response_action": "errors", "errors": {"feedback_comment": "Feedback data not found or expired."}}
            
            # Extract all the data we need
            channel = feedback_data.get("channel")
            message_ts = feedback_data.get("message_ts")
            thread_ts = feedback_data.get("thread_ts")
            sql_query = feedback_data.get("sql_query")
            is_correct = feedback_data.get("is_correct")
            message_blocks = feedback_data.get("message_blocks", [])
            
            # Get the user's comment
            comment = ""
            state_values = view.get("state", {}).get("values", {})
            if "feedback_comment" in state_values and "comment" in state_values["feedback_comment"]:
                comment = state_values["feedback_comment"]["comment"].get("value", "")
            
            # Fetch the conversation history from the thread
            conversation_context = ""
            try:
                # If we have thread_ts, use it to find all relevant messages
                if thread_ts:
                    # Get the conversation history
                    conversation = client.conversations_replies(
                        channel=channel,
                        ts=thread_ts,
                        limit=50
                    )
                    
                    # Collect all non-bot messages that occurred before the bot's response
                    user_messages = []
        
                    for msg in conversation.get("messages", []):
                        # Skip bot messages
                        if "bot_id" not in msg and msg.get("user") != BOT_USER_ID:
                            # Stop when we reach the message being evaluated
                            if msg.get("ts") == message_ts:
                                break
                                
                            # Extract the question
                            raw_text = msg.get("text", "")
                            clean_text = extract_question_from_message(raw_text, BOT_USER_ID)
                            
                            if clean_text:
                                user_messages.append(clean_text)
                    
                    # Join all user messages
                    if user_messages:
                        conversation_context = " |<--||-->| ".join(user_messages)
                    
                if not conversation_context:
                    conversation_context = "[Original conversation not found]"
            except Exception as e:
                logger.error(f"Error fetching conversation context: {e}")
                conversation_context = f"[Error retrieving conversation context: {str(e)}]"
            
            # Save feedback with the additional comment
            result, error = await save_feedback_and_execute_sql(
                question=conversation_context, 
                sql_query=sql_query, 
                feedback=is_correct,
                comment=comment
            )
            
            if error:
                logger.error(f"Error saving feedback: {error}")
                return {"response_action": "errors", "errors": {"feedback_comment": "Failed to save feedback. Please try again."}}
            
            # Update the message with a new approach
            try:
                # Create a new array for the updated blocks
                updated_blocks = []
                
                # Keep track of feedback blocks to remove
                for block in message_blocks:
                    # Skip the feedback buttons block
                    if block.get("block_id") == "feedback_buttons":
                        continue
                    if block.get("block_id") == "added_feedback_message":
                        continue
                    # Skip the "Was this response helpful?" section
                    # Keep all other blocks
                    updated_blocks.append(block)
                
                # Add the feedback confirmation block at the end
                feedback_text = f"✅ Thanks for your feedback. Data bot's current accuracy is 88% with your help we are aiming for 99%"
                
                updated_blocks.append({
                    "type": "context",
                    "elements": [
                        {
                            "type": "mrkdwn",
                            "text": feedback_text
                        }
                    ]
                })
                
                # Extract original text content for fallback
                original_text = payload.get("message", {}).get("text", "SQL query with feedback")
                
                # Update the message with the modified blocks
                client.chat_update(
                    channel=channel,
                    ts=message_ts,
                    blocks=updated_blocks,
                    text=original_text  # Use original text as fallback
                )
                
                # Clean up the stored feedback data
                await delete_all_feedback_data()
                
                # Log success
                logger.info(f"Successfully processed feedback {feedback_id}")
                
            except Exception as e:
                logger.error(f"Error updating message after modal submission: {e}")
                # Even if we fail to update, still acknowledge submission and clean up
                await delete_all_feedback_data()
            
            # Close the modal
            return {"response_action": "clear"}
        
        return {"status": "ignored"}

    except Exception as e:
        logger.error(f"Error handling interaction: {e}")
        return {"status": "error", "detail": str(e)}
    finally:
        delete_all_feedback_data

