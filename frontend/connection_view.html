<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Configure Data Source - AfriexCopilot</title>
    <link rel="stylesheet" href="/static/css/connections.css">
    <link rel="stylesheet" href="/static/css/connection_view_light.css">
</head>
<body>
    <div id="sidebar" class="sidebar">
        <div class="sidebar-header">
            <div class="logo">
                <div class="logo-icon"></div>
                AfriexCopilot
            </div>
        </div>
        <nav class="sidebar-nav">
            <a href="dashboard" class="nav-item">
                <div class="nav-icon">📊</div>
                Dashboard
            </a>
            <a href="connections" class="nav-item">
                <div class="nav-icon">🔗</div>
                Connections
            </a>
            <a href="settings" class="nav-item">
                <div class="nav-icon">⚙️</div>
                Settings
            </a>
        </nav>
        <div class="sidebar-footer">
            <div class="user-profile">
                <div class="user-avatar">{{ user.full_name[0]|upper if user.full_name else 'U' }}</div>
                <div class="user-info">
                    <div class="user-name">{{ user.full_name or 'User' }}</div>
                    <div class="company-name">{{ user.organization_name or '' }}</div>
                </div>
            </div>
        </div>
    </div>
    <div class="main-content light-mode">
        <button id="sidebar-toggle" class="sidebar-toggle">☰</button>
        <div class="content-area">
            <div class="page-header">
                <h1 class="page-title">Configure Data Source</h1>
                <div class="data-source-name">{{ data_source_name }}
                  <button id="disconnect-btn" style="margin-left:24px; padding:8px 18px; border-radius:8px; border:none; background:#dc2626; color:#fff; font-weight:600; font-size:1rem; cursor:pointer;">Disconnect</button>
                </div>
                <textarea id="data-source-description" class="data-source-description" placeholder="You can edit the description of this data source here.">{{ data_source_description }}</textarea>
            </div>
            <div class="tabs">
                <button class="tab active" data-tab="fields">Fields</button>
                <button class="tab" data-tab="access">Access</button>
                <button class="tab" data-tab="queries">Queries</button>
                <button class="tab" data-tab="version">Version History</button>
            </div>
            <div class="tab-content" id="fields-tab">
                <div class="fields-toolbar">
                    <select id="table-select" style="margin-right:16px; padding:7px 12px; border-radius:8px; border:1px solid #e5e7eb; background:#fff; font-size:1rem;"></select>
                    <button id="table-edit-btn" style="margin-right:24px; padding:8px 18px; border-radius:8px; border:none; background:#e5e7eb; color:#374151; font-weight:600; font-size:1rem; display:inline-flex; align-items:center; gap:6px;"><span style='font-size:1.1em;'>✏️</span> Edit</button>
                    <label style="display:flex; align-items:center; gap:6px; cursor:pointer; margin-right:16px;">
                        <input type="checkbox" id="table-status-checkbox" disabled>
                        <span style="color:#6b7280;">Active</span>
                    </label>
                    <input type="text" id="fields-search" class="fields-search" placeholder="Search..">
                </div>
                <div class="fields-table-container">
                    <table class="fields-table">
                        <thead>
                            <tr>
                                <th><input type="checkbox" id="select-all-fields"></th>
                                <th>NAME</th>
                                <th>DESCRIPTION</th>
                                <th>SAMPLE</th>
                            </tr>
                        </thead>
                        <tbody id="fields-table-body">
                            <!-- Populated by JS -->
                        </tbody>
                    </table>
                </div>
                <div class="pagination-controls" id="pagination-controls"></div>
            </div>
            <div class="tab-content" id="access-tab" style="display:none;">Access tab coming soon.</div>
            <div class="tab-content" id="queries-tab" style="display:none;">Queries tab coming soon.</div>
            <div class="tab-content" id="version-tab" style="display:none;">Version history coming soon.</div>
        </div>
    </div>
    <!-- Table Edit Modal -->
    <div id="table-edit-modal" style="display:none; position:fixed; top:0; left:0; width:100vw; height:100vh; background:rgba(0,0,0,0.18); z-index:2000; align-items:center; justify-content:center;">
      <div style="background:#fff; border-radius:14px; padding:32px 24px 24px 24px; max-width:420px; width:90vw; margin:auto; position:relative; box-shadow:0 8px 32px rgba(0,0,0,0.18); display:flex; flex-direction:column;">
        <h2 style="margin-bottom:18px; font-size:1.3rem; font-weight:600; color:#1f2937; text-align:center;">Edit Table</h2>
        <label style="font-weight:600; color:#374151; margin-bottom:6px;">Description</label>
        <textarea id="modal-table-description" style="width:100%; min-height:60px; border-radius:8px; border:1px solid #e5e7eb; padding:10px; font-size:1rem; margin-bottom:18px;"></textarea>
        <label style="display:flex; align-items:center; gap:8px; margin-bottom:18px;">
          <input type="checkbox" id="modal-table-status">
          <span style="color:#6b7280;">Active</span>
        </label>
        <div style="display:flex; gap:16px; justify-content:flex-end;">
          <button id="modal-cancel-btn" style="padding:8px 18px; border-radius:8px; border:none; background:#e5e7eb; color:#374151; font-weight:600; font-size:1rem;">Cancel</button>
          <button id="modal-save-btn" style="padding:8px 18px; border-radius:8px; border:none; background:#6366f1; color:#fff; font-weight:600; font-size:1rem;">Save</button>
        </div>
      </div>
    </div>
    <!-- Disconnect Confirmation Modal -->
    <div id="disconnect-modal" style="display:none; position:fixed; top:0; left:0; width:100vw; height:100vh; background:rgba(0,0,0,0.18); z-index:3000; align-items:center; justify-content:center;">
      <div style="background:#fff; border-radius:14px; padding:32px 24px 24px 24px; max-width:420px; width:90vw; margin:auto; position:relative; box-shadow:0 8px 32px rgba(0,0,0,0.18); display:flex; flex-direction:column;">
        <h2 style="margin-bottom:18px; font-size:1.3rem; font-weight:600; color:#1f2937; text-align:center;">Disconnect Data Source</h2>
        <div style="color:#dc2626; font-size:1rem; margin-bottom:18px;">Are you sure you want to disconnect? This will <b>delete all files, tables, and schema structure</b> for this connection. This action cannot be undone.</div>
        <div style="display:flex; gap:16px; justify-content:flex-end;">
          <button id="disconnect-cancel-btn" style="padding:8px 18px; border-radius:8px; border:none; background:#e5e7eb; color:#374151; font-weight:600; font-size:1rem;">Cancel</button>
          <button id="disconnect-confirm-btn" style="padding:8px 18px; border-radius:8px; border:none; background:#dc2626; color:#fff; font-weight:600; font-size:1rem;">Disconnect</button>
        </div>
      </div>
    </div>
    <script src="/static/js/connection_view_light.js"></script>
</body>
</html> 