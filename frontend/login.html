<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - AfriexCopilot</title>
    <link rel="stylesheet" href="/static/css/login.css">
</head>
<body>
    <header class="header">
        <div class="logo">Index</div>
        <nav class="header-nav">
            <a href="#" class="nav-link">Features</a>
            <a href="#" class="nav-link">Pricing</a>
            <a href="#" class="nav-link">FAQ</a>
            <a href="#" class="nav-link">Contact</a>
            <div class="auth-buttons">
                <a href="/login" class="login-btn-header">Log in</a>
                <a href="/signup" class="register-btn-header">Sign up</a>
            </div>
        </nav>
    </header>

    <main class="main-content">
        <div class="login-container">
            <div class="login-header">
                <h1 class="login-title">Login</h1>
                <p class="login-subtitle">Enter your credentials to access your account.</p>
            </div>

            <form class="login-form" id="loginForm">
                <div class="form-group">
                    <label for="email" class="form-label">Email</label>
                    <input 
                        type="email" 
                        id="email"
                        name="email" 
                        class="form-input" 
                        placeholder="<EMAIL>"
                        required
                    >
                </div>

                <div class="form-group">
                    <label for="password" class="form-label">Password</label>
                    <input 
                        type="password" 
                        id="password"
                        name="password" 
                        class="form-input" 
                        placeholder="••••••••"
                        required
                    >
                    <div class="password-requirements">
                        Password must be at least 10 characters
                    </div>
                </div>

                <button type="submit" class="login-btn">
                    Login
                </button>

                <div class="signup-link">
                    Don't have an account? <a href="/signup">Sign up</a>
                </div>
            </form>
        </div>
    </main>

    <footer class="footer">
        <div class="footer-content">
            <div class="footer-section">
                <h3>Product</h3>
                <ul>
                    <li><a href="#">Features</a></li>
                    <li><a href="#">Pricing</a></li>
                </ul>
            </div>
            <div class="footer-section">
                <h3>Resources</h3>
                <ul>
                    <li><a href="#">Documentation</a></li>
                    <li><a href="#">Guides</a></li>
                </ul>
            </div>
            <div class="footer-section">
                <h3>Company</h3>
                <ul>
                    <li><a href="#">About Us</a></li>
                    <li><a href="#">Blog</a></li>
                </ul>
            </div>
            <div class="footer-section">
                <h3>Legal</h3>
                <ul>
                    <li><a href="#">Privacy Policy</a></li>
                    <li><a href="#">Terms of Service</a></li>
                </ul>
            </div>
        </div>
    </footer>

    <div id="toast" class="toast">
        <span id="toast-message"></span>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const submitBtn = this.querySelector('.login-btn');
            const originalText = submitBtn.textContent;
            
            try {
                submitBtn.textContent = 'Logging in...';
                submitBtn.disabled = true;
                
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: formData.get('email'),
                        password: formData.get('password')
                    })
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    // Store the access token in a cookie
                    document.cookie = `access_token=${result.access_token}; path=/; max-age=86400; SameSite=Strict`;
                    // Redirect to dashboard
                    window.location.href = '/dashboard';
                } else {
                    showToast(result.detail || 'Login failed. Please check your credentials.', 'error');
                }
            } catch (error) {
                console.error('Error:', error);
                showToast('An error occurred. Please try again.', 'error');
            } finally {
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            }
        });

        // Toast notification logic
        function showToast(message, type = 'error') {
            const toast = document.getElementById('toast');
            const toastMsg = document.getElementById('toast-message');
            toastMsg.textContent = message;
            toast.className = `toast ${type === 'success' ? 'success' : ''}`;
            toast.style.display = 'block';
            toast.style.opacity = '1';
            toast.style.top = '30px';
            setTimeout(() => {
                toast.style.opacity = '0';
                toast.style.top = '0px';
                setTimeout(() => { toast.style.display = 'none'; }, 400);
            }, 3500);
        }

        // Add password validation
        document.getElementById('password').addEventListener('input', function() {
            const requirements = document.querySelector('.password-requirements');
            if (this.value.length >= 10) {
                requirements.style.color = '#38a169';
                requirements.textContent = 'Password meets requirements';
            } else {
                requirements.style.color = '#e53e3e';
                requirements.textContent = 'Password must be at least 10 characters';
            }
        });
    </script>
</body>
</html>