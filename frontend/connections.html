<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connections - AfriexCopilot</title>
    <link rel="stylesheet" href="/static/css/connections.css">
</head>
<body>
    <div class="sidebar">
        <div class="sidebar-header">
            <div class="logo">
                <div class="logo-icon"></div>
                AfriexCopilot
            </div>
        </div>

        <nav class="sidebar-nav">
            <a href="dashboard" class="nav-item">
                <div class="nav-icon">📊</div>
                Dashboard
            </a>
            <a href="#" class="nav-item active">
                <div class="nav-icon">🔗</div>
                Connections
            </a>
            <a href="#" class="nav-item">
                <div class="nav-icon">⚙️</div>
                Settings
            </a>
        </nav>

        <div class="sidebar-footer">
            <div class="user-profile">
                <div class="user-avatar">{{ user.full_name[0]|upper if user.full_name else 'U' }}</div>
                <div class="user-info">
                    <div class="user-name">{{ user.full_name or 'User' }}</div>
                    <div class="company-name">{{ user.organization_name or '' }}</div>
                </div>
            </div>
        </div>
    </div>

    <div class="main-content">
        <div class="content-area">
            <div class="page-header">
                <h1 class="page-title">Connections</h1>
                <p class="page-subtitle">Connect your data sources and communication platforms to analyse your data.</p>
            </div>

            <div class="connection-tabs">
                <a href="#" class="tab active">
                    <div class="tab-icon">📊</div>
                    Data Sources
                </a>
                <a href="#" class="tab">
                    <div class="tab-icon">💬</div>
                    Messaging
                </a>
            </div>

            <div class="connections-grid">
                <div class="connection-card" id="snowflake-card">
                    <div class="connection-icon snowflake">
                      <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
                        <circle cx="16" cy="16" r="16" fill="#29B5E8"/>
                        <path d="M16 8v16M8 16h16M11 11l10 10M21 11l-10 10" stroke="#fff" stroke-width="2" stroke-linecap="round"/>
                      </svg>
                    </div>
                    <h3 class="connection-name">Snowflake</h3>
                    <p class="connection-description">Connect to your Snowflake data warehouse.</p>
                    <a href="#" class="connection-button primary" id="snowflake-connect-btn">
                        <div class="button-icon">🔗</div>
                        Connect
                    </a>
                    <div class="card-options" style="display:none; position:relative; display:inline-block; vertical-align:middle; margin-left:8px;">
                        <button class="options-btn" style="background:none; border:none; font-size:22px; color:#6b7280; cursor:pointer; padding:2px 6px; border-radius:4px;">&#8942;</button>
                        <div class="options-dropdown" style="display:none; position:absolute; right:0; top:28px; background:#fff; border:1px solid #e5e7eb; border-radius:8px; box-shadow:0 4px 16px rgba(0,0,0,0.08); min-width:120px; z-index:10;">
                            <button class="dropdown-disconnect" style="width:100%; background:none; border:none; color:#dc2626; font-weight:500; font-size:15px; padding:10px 18px; text-align:left; border-radius:8px; cursor:pointer;">Disconnect</button>
                        </div>
                    </div>
                </div>

                <div class="connection-card">
                    <div class="connection-icon postgresql">
                      <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
                        <circle cx="16" cy="16" r="16" fill="#336791"/>
                        <path d="M10 22c0-6 12-6 12 0" stroke="#fff" stroke-width="2"/>
                        <ellipse cx="16" cy="14" rx="6" ry="4" fill="#fff"/>
                        <ellipse cx="14" cy="14" rx="1" ry="2" fill="#336791"/>
                        <ellipse cx="18" cy="14" rx="1" ry="2" fill="#336791"/>
                      </svg>
                    </div>
                    <h3 class="connection-name">PostgreSQL</h3>
                    <p class="connection-description">Connect to your PostgreSQL database instances.</p>
                    <a href="#" class="connection-button primary">
                        <div class="button-icon">🔗</div>
                        Connect
                    </a>
                    <div class="card-options" style="display:none; position:relative; display:inline-block; vertical-align:middle; margin-left:8px;">
                        <button class="options-btn" style="background:none; border:none; font-size:22px; color:#6b7280; cursor:pointer; padding:2px 6px; border-radius:4px;">&#8942;</button>
                        <div class="options-dropdown" style="display:none; position:absolute; right:0; top:28px; background:#fff; border:1px solid #e5e7eb; border-radius:8px; box-shadow:0 4px 16px rgba(0,0,0,0.08); min-width:120px; z-index:10;">
                            <button class="dropdown-disconnect" style="width:100%; background:none; border:none; color:#dc2626; font-weight:500; font-size:15px; padding:10px 18px; text-align:left; border-radius:8px; cursor:pointer;">Disconnect</button>
                        </div>
                    </div>
                </div>

                <div class="connection-card">
                    <div class="connection-icon mysql">
                      <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
                        <circle cx="16" cy="16" r="16" fill="#F29111"/>
                        <path d="M10 22c0-6 12-6 12 0" stroke="#fff" stroke-width="2"/>
                        <ellipse cx="16" cy="14" rx="6" ry="4" fill="#fff"/>
                      </svg>
                    </div>
                    <h3 class="connection-name">MySQL</h3>
                    <p class="connection-description">Connect to your MySQL database instances.</p>
                    <a href="#" class="connection-button primary">
                        <div class="button-icon">🔗</div>
                        Connect
                    </a>
                    <div class="card-options" style="display:none; position:relative; display:inline-block; vertical-align:middle; margin-left:8px;">
                        <button class="options-btn" style="background:none; border:none; font-size:22px; color:#6b7280; cursor:pointer; padding:2px 6px; border-radius:4px;">&#8942;</button>
                        <div class="options-dropdown" style="display:none; position:absolute; right:0; top:28px; background:#fff; border:1px solid #e5e7eb; border-radius:8px; box-shadow:0 4px 16px rgba(0,0,0,0.08); min-width:120px; z-index:10;">
                            <button class="dropdown-disconnect" style="width:100%; background:none; border:none; color:#dc2626; font-weight:500; font-size:15px; padding:10px 18px; text-align:left; border-radius:8px; cursor:pointer;">Disconnect</button>
                        </div>
                    </div>
                </div>

                <div class="connection-card">
                    <div class="connection-icon mongodb">
                      <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
                        <circle cx="16" cy="16" r="16" fill="#47A248"/>
                        <path d="M16 8c2 6 2 10 0 16-2-6-2-10 0-16z" fill="#fff"/>
                      </svg>
                    </div>
                    <h3 class="connection-name">MongoDB</h3>
                    <p class="connection-description">Connect to your MongoDB database (Coming Soon).</p>
                    <a href="#" class="connection-button secondary">
                        <div class="button-icon">⏰</div>
                        Coming Soon
                    </a>
                    <div class="card-options" style="display:none; position:relative; display:inline-block; vertical-align:middle; margin-left:8px;">
                        <button class="options-btn" style="background:none; border:none; font-size:22px; color:#6b7280; cursor:pointer; padding:2px 6px; border-radius:4px;">&#8942;</button>
                        <div class="options-dropdown" style="display:none; position:absolute; right:0; top:28px; background:#fff; border:1px solid #e5e7eb; border-radius:8px; box-shadow:0 4px 16px rgba(0,0,0,0.08); min-width:120px; z-index:10;">
                            <button class="dropdown-disconnect" style="width:100%; background:none; border:none; color:#dc2626; font-weight:500; font-size:15px; padding:10px 18px; text-align:left; border-radius:8px; cursor:pointer;">Disconnect</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Snowflake Modal -->
    <div id="snowflake-modal" style="display:none; position:fixed; top:0; left:0; width:100vw; height:100vh; background:rgba(0,0,0,0.3); z-index:1000; align-items:center; justify-content:center;">
      <div id="snowflake-modal-content" style="background:white; border-radius:14px; padding:32px 24px 24px 24px; max-width:420px; width:90vw; margin:auto; position:relative; box-shadow:0 8px 32px rgba(0,0,0,0.18); display:flex; flex-direction:column; max-height: 90vh; overflow-y: auto;">
        <button id="close-snowflake-modal" style="position:absolute; top:12px; right:12px; background:none; border:none; font-size:24px; cursor:pointer; color:#6b7280;">&times;</button>
        <h2 id="db-modal-title" style="margin-bottom:18px; font-size:1.5rem; font-weight:600; color:#1f2937; text-align:center;">Connect to Snowflake</h2>
        <form id="snowflake-form" style="display:flex; flex-direction:column; gap:12px;">
          <!-- Snowflake only -->
          <div data-db="snowflake">
            <label>Account:</label><br>
            <input type="text" name="account" style="width:100%; padding:8px; border-radius:6px; border:1px solid #e5e7eb;">
          </div>
          <div data-db="snowflake">
            <label>Role:</label><br>
            <input type="text" name="role" style="width:100%; padding:8px; border-radius:6px; border:1px solid #e5e7eb;">
          </div>
          <div data-db="snowflake">
            <label>Warehouse:</label><br>
            <input type="text" name="warehouse" style="width:100%; padding:8px; border-radius:6px; border:1px solid #e5e7eb;">
          </div>
          <!-- Common fields for all DBs -->
          <div data-db="all">
            <label>User:</label><br>
            <input type="text" name="user" style="width:100%; padding:8px; border-radius:6px; border:1px solid #e5e7eb;">
          </div>
          <div data-db="all">
            <label>Password:</label><br>
            <input type="password" name="password" style="width:100%; padding:8px; border-radius:6px; border:1px solid #e5e7eb;">
          </div>
          <div data-db="all">
            <label>Database:</label><br>
            <input type="text" name="database" style="width:100%; padding:8px; border-radius:6px; border:1px solid #e5e7eb;">
          </div>
          <!-- MySQL/Postgres only -->
          <div data-db="mysql,postgres,snowflake">
            <label>Host:</label><br>
            <input type="text" name="host" style="width:100%; padding:8px; border-radius:6px; border:1px solid #e5e7eb;">
          </div>
          <div data-db="mysql,postgres,snowflake">
            <label>Port:</label><br>
            <input type="text" name="port" style="width:100%; padding:8px; border-radius:6px; border:1px solid #e5e7eb;">
          </div>
          <div id="schemas-section" style="margin-top:8px; display:none;"></div>
          <button type="submit" id="connect-snowflake-btn" style="background:#6366f1; color:white; border:none; padding:10px 20px; border-radius:6px; font-weight:600; cursor:pointer;"><span class="button-spinner" style="display:none"></span>Connect</button>
          <button type="button" id="submit-snowflake-btn" style="background:#29b5e8; color:white; border:none; padding:10px 20px; border-radius:6px; font-weight:600; cursor:pointer; display:none;"><span class="button-spinner" style="display:none"></span>Submit</button>
        </form>
        <div id="snowflake-status" style="margin-top:16px; color:#1f2937; min-height:32px; max-height:90px; overflow-y:auto; word-break:break-word; white-space:pre-line; font-size:15px; text-align:left;"></div>
      </div>
    </div>
    <!-- Terminal Log Modal -->
    <div id="log-modal" style="display:none; position:fixed; top:0; left:0; width:100vw; height:100vh; background:rgba(0,0,0,0.7); z-index:2000; align-items:center; justify-content:center;">
      <div id="log-modal-content" style="background:#18181b; border-radius:14px; padding:32px 24px 24px 24px; max-width:700px; width:95vw; margin:auto; position:relative; box-shadow:0 8px 32px rgba(0,0,0,0.28); display:flex; flex-direction:column; min-height:400px;">
        <button id="close-log-modal" style="position:absolute; top:12px; right:12px; background:none; border:none; font-size:24px; cursor:pointer; color:#a1a1aa;">&times;</button>
        <h2 style="margin-bottom:18px; font-size:1.3rem; font-weight:600; color:#fafafa; text-align:center;">Hang Tight, </h2>
        <div id="terminal-log" style="background:#23272e; color:#fafafa; font-family:monospace; border-radius:8px; padding:18px; font-size:15px; min-height:220px; max-height:350px; overflow-y:auto; box-shadow:0 2px 8px rgba(0,0,0,0.12);"></div>
      </div>
    </div>
    <!-- Disconnect Confirmation Modal -->
    <div id="disconnect-modal" style="display:none; position:fixed; top:0; left:0; width:100vw; height:100vh; background:rgba(0,0,0,0.18); z-index:3000; align-items:center; justify-content:center;">
      <div style="background:#fff; border-radius:14px; padding:32px 24px 24px 24px; max-width:420px; width:90vw; margin:auto; position:relative; box-shadow:0 8px 32px rgba(0,0,0,0.18); display:flex; flex-direction:column;">
        <h2 style="margin-bottom:18px; font-size:1.3rem; font-weight:600; color:#1f2937; text-align:center;">Disconnect Data Source</h2>
        <div style="color:#dc2626; font-size:1rem; margin-bottom:18px;">Are you sure you want to disconnect? This will <b>delete all files, tables, and schema structure</b> for this connection. This action cannot be undone.</div>
        <div style="display:flex; gap:16px; justify-content:flex-end;">
          <button id="disconnect-cancel-btn" style="padding:8px 18px; border-radius:8px; border:none; background:#e5e7eb; color:#374151; font-weight:600; font-size:1rem;">Cancel</button>
          <button id="disconnect-confirm-btn" style="padding:8px 18px; border-radius:8px; border:none; background:#dc2626; color:#fff; font-weight:600; font-size:1rem;">Disconnect</button>
        </div>
      </div>
    </div>
    <style>
      @media (max-width: 600px) {
        #snowflake-modal-content {
          max-width: 98vw !important;
          padding: 18px 6vw 18px 6vw !important;
        }
        #close-snowflake-modal {
          top: 6px !important;
          right: 6px !important;
          font-size: 22px !important;
        }
      }
    </style>

    <script src="/static/js/connections.js"></script>
</body>
</html>