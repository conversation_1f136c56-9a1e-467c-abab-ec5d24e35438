<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Settings</title>
    <link rel="stylesheet" href="/static/styles.css">
</head>
<body>
    <h2>Account Settings</h2>
    {% if success %}
        <div style="color: green;">{{ success }}</div>
    {% endif %}
    {% if error %}
        <div style="color: red;">{{ error }}</div>
    {% endif %}
    <form method="post" action="/settings">
        <label for="full_name">Full Name:</label><br>
        <input type="text" id="full_name" name="full_name" value="{{ user.full_name }}" required><br><br>

        <label for="email">Email:</label><br>
        <input type="email" id="email" name="email" value="{{ user.email }}" required><br><br>

        <label for="organization_name">Organization Name:</label><br>
        <input type="text" id="organization_name" name="organization_name" value="{{ user.organization_name }}" required><br><br>

        <label for="organization_description">Organization Description:</label><br>
        <input type="text" id="organization_description" name="organization_description" value="{{ user.organization_description or '' }}"><br><br>

        <button type="submit">Update</button>
    </form>
    <br>
    <a href="/dashboard">Back to Dashboard</a>
    <a href="/signup" class="register-btn">Sign up</a>
    <a href="/signup" class="nav-link">Sign up</a>
</body>
</html> 