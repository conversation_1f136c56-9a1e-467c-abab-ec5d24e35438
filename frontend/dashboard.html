<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - AfriexCopilot</title>
    <link rel="stylesheet" href="/static/css/dashboard.css">
</head>
<body>
    <div class="sidebar">
        <div class="sidebar-header">
            <div class="logo">
                <div class="logo-icon"></div>
                Index
            </div>
        </div>

        <nav class="sidebar-nav">
            <a href="#" class="nav-item active">
                <div class="nav-icon">📊</div>
                Dashboard
            </a>
            <a href="/connections" class="nav-item">
                <div class="nav-icon">🔗</div>
                Connections
            </a>
            <a href="#" class="nav-item">
                <div class="nav-icon">⚙️</div>
                Settings
            </a>
        </nav>

        <div class="sidebar-footer">
            <div class="user-profile">
                <div class="user-avatar">{{ user.full_name[0]|upper if user.full_name else 'U' }}</div>
                <div class="user-info">
                    <div class="user-name">{{ user.full_name or 'User' }}</div>
                    <div class="company-name">{{ user.organization_name or '' }}</div>
                </div>
            </div>
        </div>
    </div>

    <div class="main-content">
        <div class="content-area">
            <div class="welcome-header">
                <h1 class="welcome-title">Welcome Back, {{ user.full_name.split(' ')[0] if user.full_name else 'User' }}</h1>
                <p class="welcome-subtitle">Here's an overview of your organization's data and AI interactions.</p>
            </div>

            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-header">
                        <span class="stat-title">Connected Sources</span>
                        <div class="stat-icon blue">📊</div>
                    </div>
                    <div class="stat-value">4</div>
                </div>

                <div class="stat-card">
                    <div class="stat-header">
                        <span class="stat-title">AI Queries Today</span>
                        <div class="stat-icon purple">💬</div>
                    </div>
                    <div class="stat-value">38</div>
                </div>

                <div class="stat-card">
                    <div class="stat-header">
                        <span class="stat-title">Active Users</span>
                        <div class="stat-icon green">👥</div>
                    </div>
                    <div class="stat-value">17</div>
                </div>

                <div class="stat-card">
                    <div class="stat-header">
                        <span class="stat-title">Query Growth</span>
                        <div class="stat-icon yellow">📈</div>
                    </div>
                    <div class="stat-value stat-growth">+24%</div>
                </div>
            </div>

            <div class="section">
                <h2 class="section-title">Connected Data Sources</h2>
                <p class="section-subtitle">Status and health of your integrated data platforms</p>
                
                <div class="data-sources-list">
                    <div class="data-source-item">
                        <div class="data-source-icon snowflake">❄️</div>
                        <div class="data-source-info">
                            <div class="data-source-name">Snowflake</div>
                            <div class="data-source-details">47 tables • Last synced 10 minutes ago</div>
                        </div>
                        <div class="data-source-status status-healthy">Healthy</div>
                    </div>

                    <div class="data-source-item">
                        <div class="data-source-icon slack">💬</div>
                        <div class="data-source-info">
                            <div class="data-source-name">Slack</div>
                            <div class="data-source-details">12 tables • Last synced 5 minutes ago</div>
                        </div>
                        <div class="data-source-status status-healthy">Healthy</div>
                    </div>

                    <div class="data-source-item">
                        <div class="data-source-icon analytics">📊</div>
                        <div class="data-source-info">
                            <div class="data-source-name">Google Analytics</div>
                            <div class="data-source-details">8 tables • Last synced 3 hours ago</div>
                        </div>
                        <div class="data-source-status status-warning">Warning</div>
                    </div>

                    <div class="data-source-item">
                        <div class="data-source-icon crm">💼</div>
                        <div class="data-source-info">
                            <div class="data-source-name">Sales CRM</div>
                            <div class="data-source-details">23 tables • Last synced 25 minutes ago</div>
                        </div>
                        <div class="data-source-status status-healthy">Healthy</div>
                    </div>
                </div>
            </div>

            <div class="section">
                <h2 class="section-title">Recent AI Queries</h2>
                <p class="section-subtitle">Top questions your team is asking the AI</p>
                
                <div class="queries-section">
                    <div class="query-item">
                        <div class="query-icon">🔍</div>
                        <div class="query-content">
                            <div class="query-text">"What were our top-selling products last quarter?"</div>
                            <div class="query-meta">
                                <span class="query-author">Sarah Johnson</span>
                                <span class="query-source">Snowflake</span>
                                <span>10 minutes ago</span>
                            </div>
                        </div>
                    </div>

                    <div class="query-item">
                        <div class="query-icon">🔍</div>
                        <div class="query-content">
                            <div class="query-text">"Our major customer feedback from last week's release?"</div>
                            <div class="query-meta">
                                <span class="query-author">Mike Chen</span>
                                <span class="query-source">Slack</span>
                                <span>25 minutes ago</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>