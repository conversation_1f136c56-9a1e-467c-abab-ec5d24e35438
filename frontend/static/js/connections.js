// Tab switching functionality
const tabs = document.querySelectorAll('.tab');
tabs.forEach(tab => {
    tab.addEventListener('click', (e) => {
        e.preventDefault();
        // Remove active class from all tabs
        tabs.forEach(t => t.classList.remove('active'));
        // Add active class to clicked tab
        tab.classList.add('active');
        // Here you could add logic to show/hide different connection types
        // For now, we'll just handle the visual state
    });
});

// --- Patch: Track connected platforms and connection state ---
let connectedPlatforms = new Set();
let connectedIds = {};

// --- Card click handler (robust) ---
const connectionCards = document.querySelectorAll('.connection-card');
connectionCards.forEach(card => {
    card.addEventListener('click', (e) => {
        // Don't trigger if clicking on any button or inside a button
        if (e.target.closest('.connection-button')) return;
        const connectionName = card.querySelector('.connection-name').textContent.trim();
        const button = card.querySelector('.connection-button');
        // Only show alert if the button says 'Coming Soon' or 'Connect' and not connected
        if (button && (button.textContent.includes('Coming Soon') || button.textContent.includes('Connect'))) {
            // For MongoDB, show coming soon
            if (connectionName === 'MongoDB') {
                alert(`${connectionName} coming soon!`);
            } else if (button.textContent.includes('Connect')) {
                // Optionally, open modal for connect (handled by button), or do nothing
            }
        }
        // If connected, do nothing (button handles redirect)
    });
});

// --- Button click handler (robust) ---
const connectionButtons = document.querySelectorAll('.connection-button');
connectionButtons.forEach(button => {
    button.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        const card = button.closest('.connection-card');
        const connectionName = card.querySelector('.connection-name').textContent.trim();
        // Prevent opening connect modal if already connected (View button)
        if (button.textContent === 'View') {
            // Redirect to connection view page (if implemented)
            const name = card.querySelector('.connection-name').textContent.trim().toLowerCase();
            let platform = name;
            if (platform === 'postgresql') platform = 'postgres';
            const connectionObjs = connected.filter(c => c.database_platform.toLowerCase() === platform);
            if (connectionObjs.length > 0) {
                // Redirect to connection view page for the first connection
                window.location.href = `/connection_view?connection_id=${connectionObjs[0].id}`;
            }
            return;
        }
        if (button.textContent.includes('Connect')) {
            if (connectionName === 'Snowflake') {
                // Open the modal directly for Snowflake
                snowflakeModal.style.display = 'flex';
                snowflakeStatus.textContent = '';
                schemasSection.style.display = 'none';
                generateSchemaBtn.style.display = 'none';
            } else {
                // Here you would typically open a modal or redirect to configuration page for other DBs
            }
        } else if (button.textContent.includes('Coming Soon')) {
            alert(`${connectionName} coming soon!`);
        }
    });
});

// Snowflake Modal Logic
const snowflakeModal = document.getElementById('snowflake-modal');
const closeSnowflakeModal = document.getElementById('close-snowflake-modal');
const snowflakeForm = document.getElementById('snowflake-form');
const snowflakeStatus = document.getElementById('snowflake-status');
const connectBtn = document.getElementById('connect-snowflake-btn');
const schemasSection = document.getElementById('schemas-section');
const generateSchemaBtn = document.getElementById('generate-schema-btn');
const submitSnowflakeBtn = document.getElementById('submit-snowflake-btn');
const logModal = document.getElementById('log-modal');
const closeLogModal = document.getElementById('close-log-modal');
const terminalLog = document.getElementById('terminal-log');

let snowflakeFormData = null;
let availableSchemas = [];

// --- WebSocket Progress UI ---
let ws = null;
let progressArea = null;
let reportArea = null;

function setupProgressUI() {
    // Create or get progress area
    progressArea = document.getElementById('schema-progress-area');
    if (!progressArea) {
        progressArea = document.createElement('div');
        progressArea.id = 'schema-progress-area';
        progressArea.style = 'margin-top:20px; background:#f3f4f6; border-radius:8px; padding:12px; font-size:14px; max-height:180px; overflow-y:auto;';
        snowflakeForm.parentNode.insertBefore(progressArea, snowflakeForm.nextSibling);
    }
    progressArea.innerHTML = '';
    // Create or get report area
    reportArea = document.getElementById('schema-report-area');
    if (!reportArea) {
        reportArea = document.createElement('div');
        reportArea.id = 'schema-report-area';
        reportArea.style = 'margin-top:16px; background:#e0f2fe; border-radius:8px; padding:12px; font-size:14px; display:none;';
        progressArea.parentNode.insertBefore(reportArea, progressArea.nextSibling);
    }
    reportArea.innerHTML = '';
    reportArea.style.display = 'none';
}

function appendProgressMessage(msg) {
    if (!progressArea) return;
    const div = document.createElement('div');
    div.textContent = msg;
    progressArea.appendChild(div);
    progressArea.scrollTop = progressArea.scrollHeight;
}

function showFinalReport(report) {
    if (!reportArea) return;
    reportArea.style.display = 'block';
    reportArea.innerHTML = '<b>Schema Generation Report:</b><br>' +
        '<pre style="white-space:pre-wrap; word-break:break-word;">' + JSON.stringify(report, null, 2) + '</pre>';
}

closeSnowflakeModal.addEventListener('click', () => {
    snowflakeModal.style.display = 'none';
});
snowflakeModal.addEventListener('click', (e) => {
    if (e.target === snowflakeModal) snowflakeModal.style.display = 'none';
});

// On form submit: validate, test connection, fetch schemas
snowflakeForm.addEventListener('submit', async (e) => {
    e.preventDefault();
    if (connectBtn.style.display === 'none') return; // Only handle connect step here
    snowflakeStatus.textContent = '';
    schemasSection.style.display = 'none';
    connectBtn.disabled = true;
    connectBtn.querySelector('.button-spinner').style.display = 'inline-block';
    // Validate fields
    const formData = new FormData(snowflakeForm);
    const requiredFields = ['account','user','password','role','warehouse','database'];
    for (const field of requiredFields) {
        if (!formData.get(field)) {
            snowflakeStatus.innerHTML = `<span style='color:#dc2626;'>Please fill in all required fields.</span>`;
            connectBtn.disabled = false;
            connectBtn.querySelector('.button-spinner').style.display = 'none';
            return;
        }
    }
    snowflakeStatus.textContent = 'Connecting...';
    snowflakeFormData = {
        backend: 'snowflake',
        connection_params: {
            account: formData.get('account'),
            user: formData.get('user'),
            password: formData.get('password'),
            role: formData.get('role'),
            warehouse: formData.get('warehouse'),
            database: formData.get('database')
        },
        optional: {}
    };
    try {
        // 1. Test connection
        const testRes = await fetch('/api/v1/db-schema/test-connection', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(snowflakeFormData)
        });
        const testResult = await testRes.json();
        if (!testRes.ok || !testResult.success) {
            snowflakeStatus.innerHTML = '<span style="color:#dc2626;">Invalid credentials. Please check your Snowflake details.</span>' +
                (testResult.error_detail ? `<div style="color:#6b7280; font-size:12px; margin-top:4px;">${testResult.error_detail}</div>` : '');
            connectBtn.disabled = false;
            connectBtn.querySelector('.button-spinner').style.display = 'none';
            return;
        }
        snowflakeStatus.textContent = 'Connection successful! Fetching schemas...';
        // 2. Fetch schemas
        const schemaRes = await fetch('/api/v1/db-schema/list-schemas', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(snowflakeFormData)
        });
        const schemaResult = await schemaRes.json();
        if (!schemaRes.ok || !schemaResult.schemas) {
            snowflakeStatus.textContent = 'Failed to fetch schemas: ' + (schemaResult.detail || 'Unknown error');
            connectBtn.disabled = false;
            connectBtn.querySelector('.button-spinner').style.display = 'none';
            return;
        }
        availableSchemas = schemaResult.schemas;
        if (availableSchemas.length === 0) {
            snowflakeStatus.textContent = 'No schemas found.';
            connectBtn.disabled = false;
            connectBtn.querySelector('.button-spinner').style.display = 'none';
            return;
        }
        // 3. Show checkboxes
        schemasSection.innerHTML = '<label style="font-weight:600; color:#374151;">Select schemas to analyze:</label><div style="margin-top:8px; display:flex; flex-wrap:wrap; gap:10px;">' +
            availableSchemas.map(s => `<label style=\"background:#f3f4f6; border-radius:6px; padding:6px 12px; margin-bottom:4px; display:flex; align-items:center; gap:6px; cursor:pointer;\"><input type=\"checkbox\" name=\"selected_schemas\" value=\"${s}\" style=\"margin-right:6px;\">${s}</label>`).join('') + '</div>';
        schemasSection.style.display = 'block';
        connectBtn.style.display = 'none';
        submitSnowflakeBtn.style.display = 'inline-block';
        snowflakeStatus.textContent = 'Select schemas and click Submit.';
    } catch (err) {
        snowflakeStatus.textContent = 'Network error: ' + err.message;
        connectBtn.disabled = false;
        connectBtn.querySelector('.button-spinner').style.display = 'none';
    }
    connectBtn.disabled = false;
    connectBtn.querySelector('.button-spinner').style.display = 'none';
});

// Enable/disable Submit button based on selection
schemasSection.addEventListener('change', (e) => {
    const checked = schemasSection.querySelectorAll('input[type=\"checkbox\"]:checked');
    submitSnowflakeBtn.disabled = checked.length === 0;
});

// On submit, start schema generation and open log modal
submitSnowflakeBtn.addEventListener('click', async (e) => {
    e.preventDefault();
    // Get selected schemas
    const selected = Array.from(schemasSection.querySelectorAll('input[type=\"checkbox\"]:checked')).map(cb => cb.value);
    if (selected.length === 0) {
        snowflakeStatus.textContent = 'Please select at least one schema.';
        return;
    }
    snowflakeStatus.textContent = 'Starting schema generation...';
    // Open log modal and clear logs
    logModal.style.display = 'flex';
    terminalLog.textContent = '';
    // Send to generate endpoint
    const data = {
        ...snowflakeFormData,
        optional: { schemas: selected }
    };
    try {
        const response = await fetch('/api/v1/db-schema/generate', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(data)
        });
        const result = await response.json();
        if (response.ok) {
            snowflakeStatus.textContent = 'Schema generation started! Request ID: ' + result.request_id;
            if (ws) { ws.close(); }
            ws = new WebSocket(`ws://${window.location.host}/ws/schema-progress/${result.request_id}`);
            ws.onmessage = (event) => {
                try {
                    const msg = event.data;
                    let parsed = null;
                    try { parsed = JSON.parse(msg); } catch {}
                    if (parsed && parsed.type === 'heartbeat') {
                        // Ignore heartbeat messages
                        return;
                    }
                    if (parsed && parsed.type === 'final_report') {
                        terminalLog.innerHTML += '<div style="color:#22d3ee;">✔️ Schema generation complete.</div>';
                        terminalLog.innerHTML += '<pre style="color:#a3e635;">' + JSON.stringify(parsed.report, null, 2) + '</pre>';
                        ws.close(); // Close the WebSocket after final report
                        // Refresh connection cards after a short delay
                        setTimeout(() => { window.location.reload(); }, 1200);
                    } else {
                        terminalLog.innerHTML += '<div>' + (parsed && parsed.message ? parsed.message : msg) + '</div>';
                    }
                } catch (err) {
                    terminalLog.innerHTML += '<div>' + event.data + '</div>';
                }
                terminalLog.scrollTop = terminalLog.scrollHeight;
            };
            ws.onclose = () => {
                terminalLog.innerHTML += '<div style="color:#f87171;">WebSocket connection closed.</div>';
            };
        } else {
            snowflakeStatus.textContent = 'Error: ' + (result.detail || result.message || 'Unknown error');
        }
    } catch (err) {
        snowflakeStatus.textContent = 'Network error: ' + err.message;
    }
});

closeLogModal.addEventListener('click', () => {
    logModal.style.display = 'none';
    if (ws) ws.close();
});
logModal.addEventListener('click', (e) => {
    if (e.target === logModal) logModal.style.display = 'none';
    if (ws) ws.close();
});

(async function() {
    // Fetch connected databases
    let connected = [];
    try {
        const res = await fetch('/api/v1/user-connections');
        if (res.ok) {
            connected = await res.json();
        }
    } catch (e) {
        console.error('Failed to fetch user connections', e);
    }

    // Map platforms for easy lookup
    connectedPlatforms = new Set(connected.map(c => c.database_platform.toLowerCase()));
    connectedIds = {};
    connected.forEach(c => {
        connectedIds[c.database_platform.toLowerCase()] = c.id;
    });

    // Update each card/button
    document.querySelectorAll('.connection-card').forEach(card => {
        const name = card.querySelector('.connection-name').textContent.trim().toLowerCase();
        let platform = name;
        if (platform === 'postgresql') platform = 'postgres'; // match backend naming
        // Find all connection objects for this platform
        const connectionObjs = connected.filter(c => c.database_platform.toLowerCase() === platform);
        const isConnected = connectionObjs.length > 0;
        const cardOptions = card.querySelector('.card-options');
        const optionsBtn = card.querySelector('.options-btn');
        const optionsDropdown = card.querySelector('.options-dropdown');
        const dropdownDisconnect = card.querySelector('.dropdown-disconnect');
        const connectBtn = card.querySelector('.connection-button');
        // Add or update Connected label
        let connectedLabel = card.querySelector('.connected-label');
        if (!connectedLabel) {
            connectedLabel = document.createElement('div');
            connectedLabel.className = 'connected-label';
            connectedLabel.style = 'color:#22c55e; font-weight:600; margin-top:8px; font-size:15px;';
            card.insertBefore(connectedLabel, connectBtn.nextSibling);
        }
        if (isConnected) {
            // Show options icon for connected
            if (cardOptions) cardOptions.style.display = 'inline-block';
            if (connectBtn) {
                connectBtn.textContent = 'View';
                connectBtn.classList.remove('primary');
                connectBtn.classList.add('secondary');
                connectBtn.style.display = 'inline-flex';
                connectBtn.onclick = function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    // Always redirect to connection view page for the first connection
                    window.location.href = `/connection_view?connection_id=${connectionObjs[0].id}`;
                };
            }
            connectedLabel.textContent = 'Connected';
            connectedLabel.style.display = 'block';
            if (optionsBtn && optionsDropdown && dropdownDisconnect) {
                optionsBtn.onclick = function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    document.querySelectorAll('.options-dropdown').forEach(dd => { if (dd !== optionsDropdown) dd.style.display = 'none'; });
                    optionsDropdown.style.display = optionsDropdown.style.display === 'block' ? 'none' : 'block';
                };
                dropdownDisconnect.onclick = function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    disconnectModal.dataset.connectionId = connectionObjs[0].id;
                    disconnectModal.style.display = 'flex';
                    optionsDropdown.style.display = 'none';
                };
            }
        } else {
            // Hide options for not connected
            if (cardOptions) cardOptions.style.display = 'none';
            if (connectBtn) {
                connectBtn.textContent = 'Connect';
                connectBtn.classList.add('primary');
                connectBtn.classList.remove('secondary');
                connectBtn.style.display = 'inline-flex';
                connectBtn.onclick = null;
            }
            connectedLabel.textContent = '';
            connectedLabel.style.display = 'none';
        }
    });
    // Hide dropdown on outside click
    document.addEventListener('click', function(e) {
        document.querySelectorAll('.options-dropdown').forEach(dd => dd.style.display = 'none');
    });
})();

// --- Generalized Modal Logic for All DBs ---
const dbModal = document.getElementById('snowflake-modal');
const closeDbModal = document.getElementById('close-snowflake-modal');
const dbForm = document.getElementById('snowflake-form');
const dbStatus = document.getElementById('snowflake-status');
const dbConnectBtn = document.getElementById('connect-snowflake-btn');
const dbSchemasSection = document.getElementById('schemas-section');
const dbSubmitBtn = document.getElementById('submit-snowflake-btn');
let dbFormData = null;
let dbAvailableSchemas = [];
let currentDbType = null;

// Helper to set up the modal for a given db type
function setupDbModal(dbType) {
    currentDbType = dbType;
    // Set modal title dynamically
    const dbModalTitle = document.getElementById('db-modal-title');
    if (dbType === 'snowflake') dbModalTitle.textContent = 'Connect to Snowflake';
    else if (dbType === 'postgres') dbModalTitle.textContent = 'Connect to PostgreSQL';
    else if (dbType === 'mysql') dbModalTitle.textContent = 'Connect to MySQL';
    else dbModalTitle.textContent = 'Connect to Database';
    // Clear form
    dbForm.reset();
    dbStatus.textContent = '';
    dbSchemasSection.style.display = 'none';
    dbConnectBtn.style.display = 'inline-block';
    dbSubmitBtn.style.display = 'none';
    // Show/hide fields based on dbType
    dbForm.querySelectorAll('div').forEach(div => div.style.display = 'none');
    if (dbType === 'snowflake') {
        dbForm.querySelectorAll('div').forEach(div => {
            const label = div.querySelector('label');
            if (!label) return;
            const text = label.textContent.toLowerCase();
            // Only show Snowflake-specific and common fields, NOT host/port
            if (["account","role","warehouse","user","password","database"].some(f => text.includes(f))) div.style.display = 'block';
        });
    } else if (dbType === 'postgres') {
        dbForm.querySelectorAll('div').forEach(div => {
            const label = div.querySelector('label');
            if (!label) return;
            const text = label.textContent.toLowerCase();
            if (["host","port","database","user","password"].some(f => text.includes(f))) div.style.display = 'block';
        });
    } else if (dbType === 'mysql') {
        dbForm.querySelectorAll('div').forEach(div => {
            const label = div.querySelector('label');
            if (!label) return;
            const text = label.textContent.toLowerCase();
            if (["host","port","database","user","password"].some(f => text.includes(f))) div.style.display = 'block';
        });
    }
}

// --- Button click handler (robust, generalized) ---
connectionButtons.forEach(button => {
    button.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        const card = button.closest('.connection-card');
        const connectionName = card.querySelector('.connection-name').textContent.trim();
        // Prevent opening connect modal if already connected (View button)
        if (button.textContent === 'View') {
            // Redirect to connection view page (if implemented)
            const name = card.querySelector('.connection-name').textContent.trim().toLowerCase();
            let platform = name;
            if (platform === 'postgresql') platform = 'postgres';
            const connectionObjs = connected.filter(c => c.database_platform.toLowerCase() === platform);
            if (connectionObjs.length > 0) {
                // Redirect to connection view page for the first connection
                window.location.href = `/connection_view?connection_id=${connectionObjs[0].id}`;
            }
            return;
        }
        if (button.textContent.includes('Connect')) {
            if (connectionName === 'Snowflake') {
                setupDbModal('snowflake');
                dbModal.style.display = 'flex';
            } else if (connectionName === 'PostgreSQL') {
                setupDbModal('postgres');
                dbModal.style.display = 'flex';
            } else if (connectionName === 'MySQL') {
                setupDbModal('mysql');
                dbModal.style.display = 'flex';
            }
        } else if (button.textContent.includes('Coming Soon')) {
            alert(`${connectionName} coming soon!`);
        }
    });
});

closeDbModal.addEventListener('click', () => {
    dbModal.style.display = 'none';
});
dbModal.addEventListener('click', (e) => {
    if (e.target === dbModal) dbModal.style.display = 'none';
});

// --- Generalized Form Submit Handler ---
dbForm.addEventListener('submit', async (e) => {
    e.preventDefault();
    if (dbConnectBtn.style.display === 'none') return;
    dbStatus.textContent = '';
    dbSchemasSection.style.display = 'none';
    dbConnectBtn.disabled = true;
    dbConnectBtn.querySelector('.button-spinner').style.display = 'inline-block';
    // Gather form data based on db type
    const formData = new FormData(dbForm);
    let backend, connection_params;
    if (currentDbType === 'snowflake') {
        const requiredFields = ['account','user','password','role','warehouse','database'];
        for (const field of requiredFields) {
            if (!formData.get(field)) {
                dbStatus.innerHTML = `<span style='color:#dc2626;'>Please fill in all required fields.</span>`;
                dbConnectBtn.disabled = false;
                dbConnectBtn.querySelector('.button-spinner').style.display = 'none';
                return;
            }
        }
        backend = 'snowflake';
        connection_params = {
            account: formData.get('account'),
            user: formData.get('user'),
            password: formData.get('password'),
            role: formData.get('role'),
            warehouse: formData.get('warehouse'),
            database: formData.get('database')
        };
    } else if (currentDbType === 'postgres') {
        const requiredFields = ['host','port','database','user','password'];
        let missingFields = [];
        for (const field of requiredFields) {
            if (!formData.get(field)) {
                missingFields.push(field.charAt(0).toUpperCase() + field.slice(1));
            }
        }
        if (missingFields.length > 0) {
            dbStatus.innerHTML = `<span style='color:#dc2626;'>Please fill in: ${missingFields.join(', ')}.</span>`;
            dbConnectBtn.disabled = false;
            dbConnectBtn.querySelector('.button-spinner').style.display = 'none';
            return;
        }
        backend = 'postgres';
        connection_params = {
            host: formData.get('host'),
            port: parseInt(formData.get('port'), 10),
            database: formData.get('database'),
            user: formData.get('user'),
            password: formData.get('password')
        };
    } else if (currentDbType === 'mysql') {
        const requiredFields = ['host','port','database','user','password'];
        let missingFields = [];
        for (const field of requiredFields) {
            if (!formData.get(field)) {
                missingFields.push(field.charAt(0).toUpperCase() + field.slice(1));
            }
        }
        if (missingFields.length > 0) {
            dbStatus.innerHTML = `<span style='color:#dc2626;'>Please fill in: ${missingFields.join(', ')}.</span>`;
            dbConnectBtn.disabled = false;
            dbConnectBtn.querySelector('.button-spinner').style.display = 'none';
            return;
        }
        backend = 'mysql';
        connection_params = {
            host: formData.get('host'),
            port: parseInt(formData.get('port'), 10),
            database: formData.get('database'), // use 'database' for mysql.connector.connect
            user: formData.get('user'),
            password: formData.get('password')
        };
    }
    dbFormData = { backend, connection_params, optional: {} };
    try {
        // 1. Test connection
        const testRes = await fetch('/api/v1/db-schema/test-connection', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(dbFormData)
        });
        const testResult = await testRes.json();
        if (!testRes.ok || !testResult.success) {
            dbStatus.innerHTML = '<span style="color:#dc2626;">Invalid credentials. Please check your details.</span>' +
                (testResult.error_detail ? `<div style="color:#6b7280; font-size:12px; margin-top:4px;">${testResult.error_detail}</div>` : '');
            dbConnectBtn.disabled = false;
            dbConnectBtn.querySelector('.button-spinner').style.display = 'none';
            return;
        }
        dbStatus.textContent = 'Connection successful!';
        // 2. Fetch schemas if needed
        if (currentDbType === 'postgres' || currentDbType === 'snowflake') {
            dbStatus.textContent = 'Connection successful! Fetching schemas...';
            const schemaRes = await fetch('/api/v1/db-schema/list-schemas', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(dbFormData)
            });
            const schemaResult = await schemaRes.json();
            if (!schemaRes.ok || !schemaResult.schemas) {
                dbStatus.textContent = 'Failed to fetch schemas: ' + (schemaResult.detail || 'Unknown error');
                dbConnectBtn.disabled = false;
                dbConnectBtn.querySelector('.button-spinner').style.display = 'none';
                return;
            }
            dbAvailableSchemas = schemaResult.schemas;
            if (dbAvailableSchemas.length === 0) {
                dbStatus.textContent = 'No schemas found.';
                dbConnectBtn.disabled = false;
                dbConnectBtn.querySelector('.button-spinner').style.display = 'none';
                return;
            }
            // Show checkboxes for schemas for both Postgres and Snowflake
            dbSchemasSection.innerHTML = '<label style="font-weight:600; color:#374151;">Select schemas to analyze:</label><div style="margin-top:8px; display:flex; flex-wrap:wrap; gap:10px;">' +
                dbAvailableSchemas.map(s => `<label style=\"background:#f3f4f6; border-radius:6px; padding:6px 12px; margin-bottom:4px; display:flex; align-items:center; gap:6px; cursor:pointer;\"><input type=\"checkbox\" name=\"selected_schemas\" value=\"${s}\" style=\"margin-right:6px;\">${s}</label>`).join('') + '</div>';
            dbSchemasSection.style.display = 'block';
            dbConnectBtn.style.display = 'none';
            dbSubmitBtn.style.display = 'inline-block';
            dbStatus.textContent = 'Select schema(s) and click Submit.';
        } else if (currentDbType === 'mysql') {
            // For MySQL, skip schema selection and proceed directly to schema generation
            dbStatus.textContent = 'Connection successful! Starting schema generation...';
            dbSchemasSection.style.display = 'none';
            dbConnectBtn.style.display = 'none';
            dbSubmitBtn.style.display = 'none';
            // Immediately start schema generation
            try {
                const response = await fetch('/api/v1/db-schema/generate', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(dbFormData)
                });
                const result = await response.json();
                if (response.ok) {
                    dbStatus.textContent = 'Schema generation started! Request ID: ' + result.request_id;
                    if (ws) { ws.close(); }
                    ws = new WebSocket(`ws://${window.location.host}/ws/schema-progress/${result.request_id}`);
                    ws.onmessage = (event) => {
                        try {
                            const msg = event.data;
                            let parsed = null;
                            try { parsed = JSON.parse(msg); } catch {}
                            if (parsed && parsed.type === 'heartbeat') {
                                // Ignore heartbeat messages
                                return;
                            }
                            if (parsed && parsed.type === 'final_report') {
                                terminalLog.innerHTML += '<div style="color:#22d3ee;">✔️ Schema generation complete.</div>';
                                terminalLog.innerHTML += '<pre style="color:#a3e635;">' + JSON.stringify(parsed.report, null, 2) + '</pre>';
                                ws.close();
                                setTimeout(() => { window.location.reload(); }, 1200);
                            } else {
                                terminalLog.innerHTML += '<div>' + (parsed && parsed.message ? parsed.message : msg) + '</div>';
                            }
                        } catch (err) {
                            terminalLog.innerHTML += '<div>' + event.data + '</div>';
                        }
                        terminalLog.scrollTop = terminalLog.scrollHeight;
                    };
                    ws.onclose = () => {
                        terminalLog.innerHTML += '<div style="color:#f87171;">WebSocket connection closed.</div>';
                    };
                    // Show log modal
                    logModal.style.display = 'flex';
                    terminalLog.textContent = '';
                } else {
                    dbStatus.textContent = 'Error: ' + (result.detail || result.message || 'Unknown error');
                }
            } catch (err) {
                dbStatus.textContent = 'Network error: ' + err.message;
            }
        }
    } catch (err) {
        dbStatus.textContent = 'Network error: ' + err.message;
        dbConnectBtn.disabled = false;
        dbConnectBtn.querySelector('.button-spinner').style.display = 'none';
    }
    dbConnectBtn.disabled = false;
    dbConnectBtn.querySelector('.button-spinner').style.display = 'none';
});

// Enable/disable Submit button based on selection (for Postgres/Snowflake)
dbSchemasSection.addEventListener('change', (e) => {
    if (currentDbType === 'postgres' || currentDbType === 'snowflake') {
        const checked = dbSchemasSection.querySelectorAll('input[type="checkbox"]:checked');
        dbSubmitBtn.disabled = checked.length === 0;
    } else {
        const checked = dbSchemasSection.querySelectorAll('input[type="radio"]:checked');
        dbSubmitBtn.disabled = checked.length === 0;
    }
});

// On submit, start schema generation and open log modal
dbSubmitBtn.addEventListener('click', async (e) => {
    e.preventDefault();
    let selectedSchemas = [];
    if (currentDbType === 'postgres' || currentDbType === 'snowflake') {
        selectedSchemas = Array.from(dbSchemasSection.querySelectorAll('input[type="checkbox"]:checked')).map(cb => cb.value);
        if (selectedSchemas.length === 0) {
            dbStatus.textContent = 'Please select at least one schema.';
            return;
        }
        dbFormData.optional = { schemas: selectedSchemas };
    } else {
        selectedSchemas = Array.from(dbSchemasSection.querySelectorAll('input[type="radio"]:checked')).map(cb => cb.value);
        if (selectedSchemas.length === 0) {
            dbStatus.textContent = 'Please select a schema.';
            return;
        }
        dbFormData.optional = { schemas: selectedSchemas };
    }
    dbStatus.textContent = 'Starting schema generation...';
    logModal.style.display = 'flex';
    terminalLog.textContent = '';
    try {
        const response = await fetch('/api/v1/db-schema/generate', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(dbFormData)
        });
        const result = await response.json();
        if (response.ok) {
            dbStatus.textContent = 'Schema generation started! Request ID: ' + result.request_id;
            if (ws) { ws.close(); }
            ws = new WebSocket(`ws://${window.location.host}/ws/schema-progress/${result.request_id}`);
            ws.onmessage = (event) => {
                try {
                    const msg = event.data;
                    let parsed = null;
                    try { parsed = JSON.parse(msg); } catch {}
                    if (parsed && parsed.type === 'heartbeat') {
                        // Ignore heartbeat messages
                        return;
                    }
                    if (parsed && parsed.type === 'final_report') {
                        terminalLog.innerHTML += '<div style="color:#22d3ee;">✔️ Schema generation complete.</div>';
                        terminalLog.innerHTML += '<pre style="color:#a3e635;">' + JSON.stringify(parsed.report, null, 2) + '</pre>';
                        ws.close(); // Close the WebSocket after final report
                        // Refresh connection cards after a short delay
                        setTimeout(() => { window.location.reload(); }, 1200);
                    } else {
                        terminalLog.innerHTML += '<div>' + (parsed && parsed.message ? parsed.message : msg) + '</div>';
                    }
                } catch (err) {
                    terminalLog.innerHTML += '<div>' + event.data + '</div>';
                }
                terminalLog.scrollTop = terminalLog.scrollHeight;
            };
            ws.onclose = () => {
                terminalLog.innerHTML += '<div style="color:#f87171;">WebSocket connection closed.</div>';
            };
        } else {
            dbStatus.textContent = 'Error: ' + (result.detail || result.message || 'Unknown error');
        }
    } catch (err) {
        dbStatus.textContent = 'Network error: ' + err.message;
    }
});

closeLogModal.addEventListener('click', () => {
    logModal.style.display = 'none';
    if (ws) ws.close();
});
logModal.addEventListener('click', (e) => {
    if (e.target === logModal) logModal.style.display = 'none';
    if (ws) ws.close();
});

// Disconnect modal logic (outside async IIFE)
const disconnectModal = document.getElementById('disconnect-modal');
const disconnectCancelBtn = document.getElementById('disconnect-cancel-btn');
const disconnectConfirmBtn = document.getElementById('disconnect-confirm-btn');
if (disconnectModal && disconnectCancelBtn && disconnectConfirmBtn) {
    disconnectCancelBtn.addEventListener('click', () => {
        disconnectModal.style.display = 'none';
    });
    disconnectModal.addEventListener('click', (e) => {
        if (e.target === disconnectModal) disconnectModal.style.display = 'none';
    });
    disconnectConfirmBtn.addEventListener('click', async () => {
        const connectionId = disconnectModal.dataset.connectionId;
        if (!connectionId) return;
        disconnectConfirmBtn.disabled = true;
        disconnectConfirmBtn.textContent = 'Disconnecting...';
        try {
            const res = await fetch(`/api/v1/connections/${connectionId}/disconnect`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
            });
            if (res.ok) {
                // Reset the card to disconnected state instead of removing it
                const card = Array.from(document.querySelectorAll('.connection-card')).find(card => {
                    const name = card.querySelector('.connection-name').textContent.trim().toLowerCase();
                    let platform = name;
                    if (platform === 'postgresql') platform = 'postgres';
                    return connectedIds[platform] == connectionId;
                });
                if (card) {
                    // Hide options menu
                    const cardOptions = card.querySelector('.card-options');
                    if (cardOptions) cardOptions.style.display = 'none';
                    // Show the connect button
                    const connectBtn = card.querySelector('.connection-button');
                    if (connectBtn) {
                        connectBtn.textContent = 'Connect';
                        connectBtn.classList.add('primary');
                        connectBtn.classList.remove('secondary');
                        connectBtn.style.display = 'inline-flex';
                    }
                    // Optionally reset description/status
                    // const desc = card.querySelector('.connection-description');
                    // if (desc) desc.textContent = 'Connect to your ... database.';
                }
            } else {
                const data = await res.json();
                alert(data.detail || 'Failed to disconnect.');
            }
        } catch (e) {
            alert('Failed to disconnect.');
        } finally {
            disconnectConfirmBtn.disabled = false;
            disconnectConfirmBtn.textContent = 'Disconnect';
            disconnectModal.style.display = 'none';
        }
    });
} 