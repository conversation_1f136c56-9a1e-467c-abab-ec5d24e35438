{"provider": "snowflake", "schemas": [{"schema_id": "e407c5ae-86ed-4e77-8323-40196c37abcb", "schema_name": "TRANSFORMED", "description": "Snowflake schema containing 13 table(s)", "table_count": 13, "tables_overview": [{"table_name": "TRANSFORMED.AUTOMATE_RATE_TIER_DISCOUNT_RULES", "description": "This table contains the following fields: created_at, code, from_asset, to_asset, company_list, percent_higher_than_best, created_by, updated_by, updated_at. The purpose of this table is to define discount rules associated with different rate tiers for various assets, allowing for automated adjustments based on specified criteria."}, {"table_name": "TRANSFORMED.AUTOMATE_RATE_TIER_DISCOUNT_LOG", "description": "This table contains the following fields: timestamp, code, from_asset, to_asset, discount, admin_discount, rule_by. The purpose of this table is to log the discount rates applied to different asset tiers within an automated system, allowing for tracking and auditing of discount applications over time."}, {"table_name": "TRANSFORMED.KYC_TIMELINES", "description": "This table contains the following fields: kyc_id, id, timestamp, state, processor, kyc_created_at, kyc_updated_at, kyc_status, user_country, title. The KYC_TIMELINES table is designed to track the various stages and updates of the Know Your Customer (KYC) process for users, capturing important timestamps and statuses related to their verification journey."}, {"table_name": "TRANSFORMED.MEDICI_BALANCES", "description": "This table contains the following fields: id, book, user_id, currency, transaction, balance, notes, created_at, expire_at. The MEDICI_BALANCES table is designed to track the financial balances associated with various users and their transactions in different currencies. It serves as a record of all monetary interactions, providing insights into user balances over time and facilitating financial management."}, {"table_name": "TRANSFORMED.OTC_VOLUMES", "description": "This table contains the following fields: id, from_symbol, to_symbol, country, rate, from_amount, to_amount, available_amount, user_id, timestamp, approved_by, submitted_by, updated_at, created_at. The OTC_VOLUMES table is designed to store information related to over-the-counter (OTC) trading volumes, including currency conversions and transaction details for users."}, {"table_name": "TRANSFORMED.KYCS", "description": "This table contains the following fields: id, created_at, updated_at, user_id, status, details, date_of_birth, id_type, provider, attempts, id_number, success_acked, updated_by, bvn, ssn_last_four, is_kyc_doc_provided, user_country. The KYCS table is designed to store information related to the Know Your Customer (KYC) process, which is essential for verifying the identity of users and ensuring compliance with regulatory requirements."}, {"table_name": "TRANSFORMED.REFERRALS_TIERS", "description": "This table contains the following fields: id, created_at, updated_at, name, cumulative_threshold, transaction_window, bonus_amount_per_referree, bonus_amount_per_referrer, referral_code, currency_code, should_automate_payout, should_skip_referrer_payout, should_skip_referree_payout, is_deactivated, admin, type. The REFERRALS_TIERS table is designed to manage and define various tiers within a referral program, detailing the conditions and rewards associated with each tier for both referrers and referees."}, {"table_name": "TRANSFORMED.REFERRALS", "description": "This table contains the following fields: id, updated_at, created_at, from_username, to_username, to_name, to_user_id, from_user_id, cumulative_transaction_amount, from_amount, to_amount, is_from_fulfilled, is_to_fulfilled, is_from_staff, year_fulfilled, status. The REFERRALS table is used to track referral transactions between users, capturing details about the referrer and the referred user, as well as the financial aspects of the referral process and its fulfillment status."}, {"table_name": "TRANSFORMED.MEDICI_TRANSACTIONS", "description": "This table contains the following fields: id, credit, debit, meta, datetime, account_path, accounts, book, memo, journal, timestamp, voided, void_reason, original_journal, user_id, currency, date, week, month. The MEDICI_TRANSACTIONS table is used to record and manage financial transactions within the Medici system, capturing details such as amounts, associated accounts, and transaction metadata for accounting and reporting purposes."}, {"table_name": "TRANSFORMED.STORE_OWNERS_PAYOUT_TRACKER", "description": "This table contains the following fields: user_id, user_full_name, user_phone, user_signup_at, store_code, store_owner_id, store_owner_full_name, store_owner_email, transaction_id, transaction_status, transaction_created_at, paid_at, username. The purpose of this table is to track the payout transactions made to store owners, including details about the users and the status of each transaction."}, {"table_name": "TRANSFORMED.METRIC_TRANSACTIONS", "description": "This table contains the following fields: id, created_at, date, week, month, duration, sender_id, sender_country, receiver_id, receiver_country, corridor_pair, type, afriex_type, processor, amount, currency, fulfillment_asset_value, fulfillment_asset, amount_usd, state, is_signup_bonus, from_to_usd, to_to_usd. The METRIC_TRANSACTIONS table is designed to store detailed records of financial transactions, capturing essential metrics for analysis and reporting purposes."}, {"table_name": "TRANSFORMED.USERS", "description": "This table contains the following fields: id, created_at, updated_at, username, country, email, phone, first_name, last_name, full_name, dob, referrer_id, kyc_status, kyc_updated_by, security_enabled, stripe_account_id, date, week, month, gender, device_os, phone_verified, tier_info, daily_limit, transaction_limit, account_username, account_referrer, kyc_verified_at, usernames, metadata. The USERS table is designed to store essential information about users of the application, including their personal details, account status, and verification information."}, {"table_name": "TRANSFORMED.TRANSACTIONS", "description": ""}]}], "tables": [{"table_id": "c2e52701-3eaa-4778-8e31-19e5930cbe0b", "table_name": "TRANSFORMED.AUTOMATE_RATE_TIER_DISCOUNT_RULES", "schema_id": "TRANSFORMED", "description": "This table contains the following fields: created_at, code, from_asset, to_asset, company_list, percent_higher_than_best, created_by, updated_by, updated_at. The purpose of this table is to define discount rules associated with different rate tiers for various assets, allowing for automated adjustments based on specified criteria.", "field_count": 9, "fields": [{"name": "created_at", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the discount rule was created."}, {"name": "code", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for the discount rule."}, {"name": "from_asset", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The starting asset type for which the discount rule applies."}, {"name": "to_asset", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The ending asset type for which the discount rule applies."}, {"name": "company_list", "data_type": "ARRAY", "is_categorical": false, "is_datetime": false, "description": "A list of companies that are eligible for the discount rule."}, {"name": "percent_higher_than_best", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": "The percentage that the rate must be higher than the best available rate to qualify for the discount."}, {"name": "created_by", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The identifier of the user who created the discount rule."}, {"name": "updated_by", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The identifier of the user who last updated the discount rule."}, {"name": "updated_at", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the discount rule was last updated."}], "sample_rows": []}, {"table_id": "bca36a96-799f-44d0-b23b-0e1d68895413", "table_name": "TRANSFORMED.AUTOMATE_RATE_TIER_DISCOUNT_LOG", "schema_id": "TRANSFORMED", "description": "This table contains the following fields: timestamp, code, from_asset, to_asset, discount, admin_discount, rule_by. The purpose of this table is to log the discount rates applied to different asset tiers within an automated system, allowing for tracking and auditing of discount applications over time.", "field_count": 7, "fields": [{"name": "timestamp", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The date and time when the discount was applied."}, {"name": "code", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "A unique identifier for the discount rule.", "categorical_values": ["CHURNED_USER", "NEW_USER"]}, {"name": "from_asset", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The asset type from which the discount is applied.", "categorical_values": ["USD"]}, {"name": "to_asset", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The asset type to which the discount is applied.", "categorical_values": ["NGN"]}, {"name": "discount", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": "The percentage or amount of discount applied."}, {"name": "admin_discount", "data_type": "NUMBER", "is_categorical": true, "is_datetime": false, "description": "Any additional discount applied by an administrator.", "categorical_values": ["0.0168", "0.0198", "0.02", "0.021", "0.03"]}, {"name": "rule_by", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The identifier of the rule or criteria that triggered the discount.", "categorical_values": ["<EMAIL>"]}], "sample_rows": "{\n  \"timestamp\": \"2025-07-15T12:30:00\",\n  \"code\": \"ACTIVE_USER\",\n  \"from_asset\": \"EUR\",\n  \"to_asset\": \"JPY\",\n  \"discount\": 0.025478123456789,\n  \"admin_discount\": 0.0154,\n  \"rule_by\": \"<EMAIL>\"\n}"}, {"table_id": "7db334e5-7dc3-46c4-9414-0400daa12950", "table_name": "TRANSFORMED.KYC_TIMELINES", "schema_id": "TRANSFORMED", "description": "This table contains the following fields: kyc_id, id, timestamp, state, processor, kyc_created_at, kyc_updated_at, kyc_status, user_country, title. The KYC_TIMELINES table is designed to track the various stages and updates of the Know Your Customer (KYC) process for users, capturing important timestamps and statuses related to their verification journey.", "field_count": 10, "fields": [{"name": "kyc_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for the KYC record."}, {"name": "id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each entry in the KYC_TIMELINES table."}, {"name": "timestamp", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The date and time when the KYC status was updated."}, {"name": "state", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The current state of the KYC process (e.g., pending, approved, rejected).", "categorical_values": ["failure", "notStarted", "pending", "rejected", "reuploadRequested", "submitted", "success"]}, {"name": "processor", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The name or identifier of the system or individual processing the KYC.", "categorical_values": ["admin", "smile", "stripe", "sumsub", "veriff"]}, {"name": "kyc_created_at", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The date and time when the KYC record was initially created."}, {"name": "kyc_updated_at", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The date and time when the KYC record was last updated."}, {"name": "kyc_status", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The overall status of the KYC process (e.g., active, inactive).", "categorical_values": ["failure", "notStarted", "pending", "rejected", "reuploadRequested", "reuploaded", "started", "submitted", "success"]}, {"name": "user_country", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The country of the user associated with the KYC record.", "categorical_values": ["AE", "AF", "AG", "AI", "AL", "AM", "AO", "AR", "AT", "AU", "AZ", "BA", "BB", "BD", "BE", "BF", "BG", "BH", "BI", "BJ", "BM", "BO", "BR", "BS", "BW", "BY", "CA", "CD", "CF", "CG", "CH", "CI", "CL", "CM", "CN", "CO", "CR", "CU", "CV", "CW", "CY", "CZ", "DE", "DJ", "DK", "DM", "DO", "DZ", "EC", "EE"]}, {"name": "title", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "A brief title or description of the KYC record.", "categorical_values": ["Date of Birth Mismatch", "Error on Veriff Session Creation", "Failed to Submit Documents to Veriff", "Failed to Upload Images to Veriff", "Name mismatch", "Receives <PERSON><PERSON><PERSON>", "Resubmission Requested", "Starts <PERSON><PERSON><PERSON> Verification", "Status updated", "Stripe Verification Completed", "<PERSON><PERSON><PERSON>", "Verification Abandoned", "Verification Approved", "Verification Declined", "Verification Expired", "Verification Started", "Verification Submitted", "failed", "in_review", "pending", "started", "submitted", "unverified", "verified"]}], "sample_rows": "{\n  \"kyc_id\": \"a1b2c3d4e5f67890abcdef12\",\n  \"id\": \"12345678-90ab-cdef-1234-567890abcdef\",\n  \"timestamp\": \"2024-12-01T12:00:00.000000\",\n  \"state\": \"success\",\n  \"processor\": \"exampleProcessor\",\n  \"kyc_created_at\": \"2024-11-29T10:00:00.000000\",\n  \"kyc_updated_at\": \"2024-12-01T14:00:00.000000\",\n  \"kyc_status\": \"success\",\n  \"user_country\": \"US\",\n  \"title\": \"Example Webhook Notification\"\n}"}, {"table_id": "6b4fda4e-6960-4137-b874-2ed0f665828f", "table_name": "TRANSFORMED.MEDICI_BALANCES", "schema_id": "TRANSFORMED", "description": "This table contains the following fields: id, book, user_id, currency, transaction, balance, notes, created_at, expire_at. The MEDICI_BALANCES table is designed to track the financial balances associated with various users and their transactions in different currencies. It serves as a record of all monetary interactions, providing insights into user balances over time and facilitating financial management.", "field_count": 9, "fields": [{"name": "id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each balance record."}, {"name": "book", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The name or identifier of the financial book or account associated with the balance.", "categorical_values": ["AfriexBook"]}, {"name": "user_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The unique identifier of the user to whom the balance belongs."}, {"name": "currency", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The currency type in which the balance is recorded.", "categorical_values": ["CAD", "CNY", "EGP", "ETB", "EUR", "GBP", "GHS", "HTG", "INR", "KES", "MGA", "MWK", "MZN", "NGN", "PKR", "RWF", "TZS", "UGX", "USD", "XAF", "XOF", "ZAR", "ZMW"]}, {"name": "transaction", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "Details of the transaction that affected the balance."}, {"name": "balance", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": "The current balance amount for the user in the specified currency."}, {"name": "notes", "data_type": "NUMBER", "is_categorical": true, "is_datetime": false, "description": "Any additional notes or comments related to the balance record.", "categorical_values": ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "50"]}, {"name": "created_at", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the balance record was created."}, {"name": "expire_at", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the balance record will expire or become invalid."}], "sample_rows": "{\n  \"id\": \"a1b2c3d4e5f67890abcdef123\",\n  \"book\": \"DummyBook\",\n  \"user_id\": \"**********abcdef123456789\",\n  \"currency\": \"EUR\",\n  \"transaction\": \"abcdef**********abcdef123456\",\n  \"balance\": 100.0,\n  \"notes\": 42,\n  \"created_at\": \"2025-04-01T12:00:00.000000\",\n  \"expire_at\": \"2025-04-03T12:00:00.000000\"\n}"}, {"table_id": "5876a7c8-edcb-4575-97c0-54527139c8aa", "table_name": "TRANSFORMED.OTC_VOLUMES", "schema_id": "TRANSFORMED", "description": "This table contains the following fields: id, from_symbol, to_symbol, country, rate, from_amount, to_amount, available_amount, user_id, timestamp, approved_by, submitted_by, updated_at, created_at. The OTC_VOLUMES table is designed to store information related to over-the-counter (OTC) trading volumes, including currency conversions and transaction details for users.", "field_count": 14, "fields": [{"name": "id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each record in the OTC_VOLUMES table."}, {"name": "from_symbol", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The currency symbol from which the amount is being converted.", "categorical_values": ["USD"]}, {"name": "to_symbol", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The currency symbol to which the amount is being converted.", "categorical_values": ["GHS", "NGN", "UGX"]}, {"name": "country", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The country associated with the currency conversion.", "categorical_values": ["GH", "NG", "UG", "US"]}, {"name": "rate", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The exchange rate applied for the conversion between from_symbol and to_symbol."}, {"name": "from_amount", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": "The amount of currency being converted from the from_symbol."}, {"name": "to_amount", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": "The resulting amount of currency after conversion to the to_symbol."}, {"name": "available_amount", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": "The amount of currency available for trading or conversion."}, {"name": "user_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The identifier of the user who initiated the transaction."}, {"name": "timestamp", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The date and time when the transaction was recorded."}, {"name": "approved_by", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "The identifier of the user who approved the transaction."}, {"name": "submitted_by", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "The identifier of the user who submitted the transaction for processing."}, {"name": "updated_at", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The date and time when the record was last updated."}, {"name": "created_at", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The date and time when the record was created."}], "sample_rows": "{\n  \"id\": \"a3f1c2d4e5b6a7c8d9e0f1g2\",\n  \"from_symbol\": \"EUR\",\n  \"to_symbol\": \"JPY\",\n  \"country\": \"FR\",\n  \"rate\": \"145.75\",\n  \"from_amount\": 50000,\n  \"to_amount\": 7287500,\n  \"available_amount\": 7287500,\n  \"user_id\": null,\n  \"timestamp\": \"2024-05-10T14:45:00\",\n  \"approved_by\": \"null\",\n  \"submitted_by\": \"{\\n  \\\"id\\\": \\\"b2c3d4e5f6a7b8c9d0e1f2g3\\\",\\n  \\\"name\\\": \\\"Akira\\\",\\n  \\\"role\\\": \\\"finance.manager\\\"\\n}\",\n  \"updated_at\": \"2024-05-20T11:15:30.123000\",\n  \"created_at\": \"2024-05-19T09:00:00.456000\"\n}"}, {"table_id": "9f304e3f-f023-455e-968e-3f6d3a4b053b", "table_name": "TRANSFORMED.KYCS", "schema_id": "TRANSFORMED", "description": "This table contains the following fields: id, created_at, updated_at, user_id, status, details, date_of_birth, id_type, provider, attempts, id_number, success_acked, updated_by, bvn, ssn_last_four, is_kyc_doc_provided, user_country. The KYCS table is designed to store information related to the Know Your Customer (KYC) process, which is essential for verifying the identity of users and ensuring compliance with regulatory requirements.", "field_count": 17, "fields": [{"name": "id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each KYC record."}, {"name": "created_at", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The timestamp when the KYC record was created."}, {"name": "updated_at", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The timestamp when the KYC record was last updated."}, {"name": "user_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The identifier for the user associated with this KYC record."}, {"name": "status", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The current status of the KYC process (e.g., pending, approved, rejected).", "categorical_values": ["failure", "notStarted", "pending", "rejected", "reuploadRequested", "reuploaded", "started", "submitted", "success"]}, {"name": "details", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "Additional details or notes regarding the KYC process."}, {"name": "date_of_birth", "data_type": "DATE", "is_categorical": false, "is_datetime": true, "description": "The date of birth of the user."}, {"name": "id_type", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The type of identification document provided (e.g., passport, driver's license).", "categorical_values": ["ALIEN_CARD", "BVN", "DRIVERS_LICENSE", "NATIONAL_ID", "NATIONAL_ID_NO_PHOTO", "NEW_VOTER_ID", "NIN", "PASSPORT", "Residence_permit", "SSNIT", "VOTER_ID", "bvn", "driverLicense", "drivers_license", "national_id", "national_id_no_photo", "new_voter_id", "nin", "passport", "residence_permit", "ssnit", "voter_id"]}, {"name": "provider", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The service provider or agency that conducted the KYC verification.", "categorical_values": ["admin", "smile", "stripe", "sumsub", "veriff"]}, {"name": "attempts", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": "The number of attempts made to complete the KYC verification."}, {"name": "id_number", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The identification number from the provided ID document."}, {"name": "success_acked", "data_type": "BOOLEAN", "is_categorical": false, "is_datetime": false, "description": "Indicates whether the KYC verification was successfully acknowledged."}, {"name": "updated_by", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The identifier of the user or system that last updated the KYC record."}, {"name": "bvn", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The Bank Verification Number associated with the user."}, {"name": "ssn_last_four", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The last four digits of the user's Social Security Number."}, {"name": "is_kyc_doc_provided", "data_type": "BOOLEAN", "is_categorical": false, "is_datetime": false, "description": "Indicates whether the user has provided the necessary KYC documentation."}, {"name": "user_country", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The country of residence of the user.", "categorical_values": ["AE", "AF", "AG", "AI", "AL", "AM", "AO", "AR", "AT", "AU", "AZ", "BA", "BB", "BD", "BE", "BF", "BG", "BH", "BI", "BJ", "BM", "BO", "BR", "BS", "BW", "BY", "CA", "CD", "CF", "CG", "CH", "CI", "CL", "CM", "CN", "CO", "CR", "CU", "CV", "CW", "CY", "CZ", "DE", "DJ", "DK", "DM", "DO", "DZ", "EC", "EE"]}], "sample_rows": "{\n  \"id\": \"a1b2c3d4e5f67890abcdef12\",\n  \"created_at\": \"2025-04-15T10:30:45.123000\",\n  \"updated_at\": \"2025-04-16T11:45:30.456000\",\n  \"user_id\": \"a1b2c3d4e5f67890abcdef12\",\n  \"status\": \"failed\",\n  \"details\": null,\n  \"date_of_birth\": \"1985-11-22\",\n  \"id_type\": \"driver_license\",\n  \"provider\": \"user\",\n  \"attempts\": null,\n  \"id_number\": \"*********\",\n  \"success_acked\": null,\n  \"updated_by\": \"abcdef**********abcdef12\",\n  \"bvn\": null,\n  \"ssn_last_four\": null,\n  \"is_kyc_doc_provided\": null,\n  \"user_country\": \"US\"\n}"}, {"table_id": "eefc7186-316c-4ecb-93ad-46b1fddf5b96", "table_name": "TRANSFORMED.REFERRALS_TIERS", "schema_id": "TRANSFORMED", "description": "This table contains the following fields: id, created_at, updated_at, name, cumulative_threshold, transaction_window, bonus_amount_per_referree, bonus_amount_per_referrer, referral_code, currency_code, should_automate_payout, should_skip_referrer_payout, should_skip_referree_payout, is_deactivated, admin, type. The REFERRALS_TIERS table is designed to manage and define various tiers within a referral program, detailing the conditions and rewards associated with each tier for both referrers and referees.", "field_count": 16, "fields": [{"name": "id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each referral tier."}, {"name": "created_at", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The timestamp when the referral tier was created."}, {"name": "updated_at", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The timestamp when the referral tier was last updated."}, {"name": "name", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The name of the referral tier."}, {"name": "cumulative_threshold", "data_type": "NUMBER", "is_categorical": true, "is_datetime": false, "description": "The cumulative amount that must be reached for the tier to be activated.", "categorical_values": ["5.0", "10.0", "20.0", "30.0", "50.0", "100.0", "150.0", "10000.0"]}, {"name": "transaction_window", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": "The time period during which transactions are counted towards the cumulative threshold."}, {"name": "bonus_amount_per_referree", "data_type": "NUMBER", "is_categorical": true, "is_datetime": false, "description": "The bonus amount awarded for each successful referral made by a referee.", "categorical_values": ["1.0", "5.0", "10.0", "15.0", "20.0", "35.0"]}, {"name": "bonus_amount_per_referrer", "data_type": "NUMBER", "is_categorical": true, "is_datetime": false, "description": "The bonus amount awarded to the referrer for each successful referral.", "categorical_values": ["1.0", "2.0", "5.0", "10.0", "15.0", "20.0", "35.0"]}, {"name": "referral_code", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "A unique code associated with the referral tier."}, {"name": "currency_code", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The currency in which bonuses are awarded.", "categorical_values": ["USD"]}, {"name": "should_automate_payout", "data_type": "BOOLEAN", "is_categorical": true, "is_datetime": false, "description": "Indicates whether payouts should be automated for this tier.", "categorical_values": ["False", "True"]}, {"name": "should_skip_referrer_payout", "data_type": "BOOLEAN", "is_categorical": true, "is_datetime": false, "description": "Indicates if the payout to the referrer should be skipped.", "categorical_values": ["False", "True"]}, {"name": "should_skip_referree_payout", "data_type": "BOOLEAN", "is_categorical": true, "is_datetime": false, "description": "Indicates if the payout to the referee should be skipped.", "categorical_values": ["False", "True"]}, {"name": "is_deactivated", "data_type": "BOOLEAN", "is_categorical": true, "is_datetime": false, "description": "A flag indicating whether the referral tier is currently active or deactivated.", "categorical_values": ["False", "True"]}, {"name": "admin", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "The identifier of the admin who created or manages the referral tier."}, {"name": "type", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The type of referral tier, which may define its characteristics or rules.", "categorical_values": ["merchant-aggregator", "referral"]}], "sample_rows": "{\n  \"id\": \"f3a2b1c4d5678e90f1234567\",\n  \"created_at\": \"2024-08-15T12:34:56.789000\",\n  \"updated_at\": \"2024-08-15T12:34:56.789000\",\n  \"name\": \"RandomName42\",\n  \"cumulative_threshold\": 15.0,\n  \"transaction_window\": 45.0,\n  \"bonus_amount_per_referree\": 10.0,\n  \"bonus_amount_per_referrer\": 7.0,\n  \"referral_code\": \"randomcode42\",\n  \"currency_code\": \"EUR\",\n  \"should_automate_payout\": false,\n  \"should_skip_referrer_payout\": false,\n  \"should_skip_referree_payout\": true,\n  \"is_deactivated\": \"null\",\n  \"admin\": \"{\\n  \\\"id\\\": \\\"a1b2c3d4e5f67890abcdef12\\\",\\n  \\\"name\\\": \\\"<PERSON><PERSON><PERSON>\\\",\\n  \\\"role\\\": \\\"marketing.director\\\"\\n}\",\n  \"type\": \"referral\"\n}"}, {"table_id": "061b2c34-2c45-4903-9fa5-111f8e77e878", "table_name": "TRANSFORMED.REFERRALS", "schema_id": "TRANSFORMED", "description": "This table contains the following fields: id, updated_at, created_at, from_username, to_username, to_name, to_user_id, from_user_id, cumulative_transaction_amount, from_amount, to_amount, is_from_fulfilled, is_to_fulfilled, is_from_staff, year_fulfilled, status. The REFERRALS table is used to track referral transactions between users, capturing details about the referrer and the referred user, as well as the financial aspects of the referral process and its fulfillment status.", "field_count": 16, "fields": [{"name": "id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each referral record."}, {"name": "updated_at", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the referral record was last updated."}, {"name": "created_at", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the referral record was created."}, {"name": "from_username", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The username of the user who made the referral."}, {"name": "to_username", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The username of the user who is being referred."}, {"name": "to_name", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The full name of the user who is being referred."}, {"name": "to_user_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The unique identifier of the user who is being referred."}, {"name": "from_user_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The unique identifier of the user who made the referral."}, {"name": "cumulative_transaction_amount", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": "The total amount of transactions associated with the referral."}, {"name": "from_amount", "data_type": "NUMBER", "is_categorical": true, "is_datetime": false, "description": "The amount of money associated with the referrer.", "categorical_values": ["0.0", "1.0", "2.0", "5.0", "10.0", "15.0", "20.0", "35.0"]}, {"name": "to_amount", "data_type": "NUMBER", "is_categorical": true, "is_datetime": false, "description": "The amount of money associated with the referred user.", "categorical_values": ["0.0", "5.0", "10.0", "15.0", "20.0", "35.0"]}, {"name": "is_from_fulfilled", "data_type": "BOOLEAN", "is_categorical": true, "is_datetime": false, "description": "A boolean indicating whether the referral from the referrer has been fulfilled.", "categorical_values": ["True"]}, {"name": "is_to_fulfilled", "data_type": "BOOLEAN", "is_categorical": true, "is_datetime": false, "description": "A boolean indicating whether the referral to the referred user has been fulfilled.", "categorical_values": ["True"]}, {"name": "is_from_staff", "data_type": "BOOLEAN", "is_categorical": true, "is_datetime": false, "description": "A boolean indicating whether the referrer is a staff member.", "categorical_values": ["False", "True"]}, {"name": "year_fulfilled", "data_type": "NUMBER", "is_categorical": true, "is_datetime": false, "description": "The year in which the referral was fulfilled.", "categorical_values": ["2023", "2024", "2025"]}, {"name": "status", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The current status of the referral (e.g., active, completed, pending).", "categorical_values": ["joined", "qualified", "resolved", "transacted"]}], "sample_rows": "{\n  \"id\": \"a1b2c3d4e5f67890abcdef12\",\n  \"updated_at\": \"2025-04-15T10:30:45.123000\",\n  \"created_at\": \"2025-02-20T08:15:30.456000\",\n  \"from_username\": \"randomuser123\",\n  \"to_username\": \"anotheruser456\",\n  \"to_name\": \"<PERSON>\",\n  \"to_user_id\": \"a1b2c3d4e5f67890abcdef34\",\n  \"from_user_id\": \"f1e2d3c4b5a67890abcdef56\",\n  \"cumulative_transaction_amount\": 200.0,\n  \"from_amount\": 10.0,\n  \"to_amount\": 10.0,\n  \"is_from_fulfilled\": true,\n  \"is_to_fulfilled\": true,\n  \"is_from_staff\": false,\n  \"year_fulfilled\": 2025,\n  \"status\": \"completed\"\n}"}, {"table_id": "899d5f01-9253-446a-80ff-514a69046b69", "table_name": "TRANSFORMED.MEDICI_TRANSACTIONS", "schema_id": "TRANSFORMED", "description": "This table contains the following fields: id, credit, debit, meta, datetime, account_path, accounts, book, memo, journal, timestamp, voided, void_reason, original_journal, user_id, currency, date, week, month. The MEDICI_TRANSACTIONS table is used to record and manage financial transactions within the Medici system, capturing details such as amounts, associated accounts, and transaction metadata for accounting and reporting purposes.", "field_count": 19, "fields": [{"name": "id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each transaction."}, {"name": "credit", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": "The amount credited in the transaction."}, {"name": "debit", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": "The amount debited in the transaction."}, {"name": "meta", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "Additional metadata related to the transaction."}, {"name": "datetime", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The date and time when the transaction occurred."}, {"name": "account_path", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "The hierarchical path of the account associated with the transaction."}, {"name": "accounts", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "A list of accounts involved in the transaction."}, {"name": "book", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The accounting book to which the transaction belongs.", "categorical_values": ["AfriexBook"]}, {"name": "memo", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "A brief note or description of the transaction."}, {"name": "journal", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The journal entry associated with the transaction."}, {"name": "timestamp", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the transaction was recorded."}, {"name": "voided", "data_type": "BOOLEAN", "is_categorical": true, "is_datetime": false, "description": "A flag indicating whether the transaction has been voided.", "categorical_values": ["True"]}, {"name": "void_reason", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The reason for voiding the transaction, if applicable."}, {"name": "original_journal", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The original journal entry before any modifications."}, {"name": "user_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The identifier of the user who created or modified the transaction."}, {"name": "currency", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The currency in which the transaction is recorded.", "categorical_values": ["CAD", "CNY", "EGP", "ETB", "EUR", "GBP", "GHS", "HTG", "INR", "KES", "MGA", "MWK", "MZN", "NGN", "PKR", "RWF", "TZS", "UGX", "USD", "XAF", "XOF", "ZAR", "ZMW"]}, {"name": "date", "data_type": "DATE", "is_categorical": false, "is_datetime": true, "description": "The date of the transaction, typically without time."}, {"name": "week", "data_type": "DATE", "is_categorical": false, "is_datetime": true, "description": "The week number of the year when the transaction occurred."}, {"name": "month", "data_type": "DATE", "is_categorical": false, "is_datetime": true, "description": "The month number of the year when the transaction occurred."}], "sample_rows": "{\n  \"id\": \"b1a2c3d4e5f67890abcdef12\",\n  \"credit\": 5000.0,\n  \"debit\": 0.0,\n  \"meta\": \"{\\n  \\\"channel\\\": \\\"CREDIT_CARD\\\",\\n  \\\"destinationAmount\\\": \\\"5000\\\",\\n  \\\"destinationAsset\\\": \\\"USD\\\",\\n  \\\"paymentMethodId\\\": \\\"b1a2c3d4e5f67890abcdef13\\\",\\n  \\\"processor\\\": \\\"PAYPAL\\\",\\n  \\\"receiverId\\\": \\\"abcdef**********abcdef12\\\",\\n  \\\"senderId\\\": \\\"**********abcdef12345678\\\",\\n  \\\"sourceAmount\\\": 5000,\\n  \\\"sourceAsset\\\": \\\"USD\\\",\\n  \\\"transactionId\\\": \\\"Payment-**********abcdef12345678-*************\\\"\\n}\",\n  \"datetime\": \"2024-10-01T12:30:00.000000\",\n  \"account_path\": \"[\\n  \\\"abcdef**********abcdef12\\\",\\n  \\\"USD\\\"\\n]\",\n  \"accounts\": \"abcdef**********abcdef12:USD\",\n  \"book\": \"SampleBook\",\n  \"memo\": \"deposit\",\n  \"journal\": \"b1a2c3d4e5f67890abcdef14\",\n  \"timestamp\": \"2024-10-01T12:30:00.000000\",\n  \"voided\": false,\n  \"void_reason\": \"null\",\n  \"original_journal\": null,\n  \"user_id\": \"abcdef**********abcdef12\",\n  \"currency\": \"USD\",\n  \"date\": \"2024-10-01\",\n  \"week\": \"2024-09-30\",\n  \"month\": \"2024-10-01\"\n}"}, {"table_id": "********-665e-43bd-b00c-7c46dda145ca", "table_name": "TRANSFORMED.STORE_OWNERS_PAYOUT_TRACKER", "schema_id": "TRANSFORMED", "description": "This table contains the following fields: user_id, user_full_name, user_phone, user_signup_at, store_code, store_owner_id, store_owner_full_name, store_owner_email, transaction_id, transaction_status, transaction_created_at, paid_at, username. The purpose of this table is to track the payout transactions made to store owners, including details about the users and the status of each transaction.", "field_count": 13, "fields": [{"name": "user_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for the user associated with the payout."}, {"name": "user_full_name", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The full name of the user receiving the payout."}, {"name": "user_phone", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The phone number of the user for contact purposes."}, {"name": "user_signup_at", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the user signed up."}, {"name": "store_code", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "A unique code representing the store associated with the payout.", "categorical_values": ["afrocan", "aio", "ameen", "berry", "bonje", "choice", "ebeano", "eko", "forks", "hairport", "linkup", "mo", "noi", "plush", "ruumors", "temmy", "up2u"]}, {"name": "store_owner_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for the store owner."}, {"name": "store_owner_full_name", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The full name of the store owner."}, {"name": "store_owner_email", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The email address of the store owner."}, {"name": "transaction_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for the transaction."}, {"name": "transaction_status", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The current status of the transaction (e.g., pending, completed, failed).", "categorical_values": ["SUCCESS"]}, {"name": "transaction_created_at", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the transaction was created."}, {"name": "paid_at", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the payout was made."}, {"name": "username", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The username of the user associated with the payout."}], "sample_rows": "{\n  \"user_id\": \"a1b2c3d4e5f67890abcdef12\",\n  \"user_full_name\": \"<PERSON>\",\n  \"user_phone\": \"**********1\",\n  \"user_signup_at\": \"2025-06-06T12:00:00.000000\",\n  \"store_code\": \"johnsstore\",\n  \"store_owner_id\": \"a1b2c3d4e5f67890abcdef34\",\n  \"store_owner_full_name\": \"<PERSON>\",\n  \"store_owner_email\": \"jane<PERSON>@example.com\",\n  \"transaction_id\": \"a1b2c3d4e5f67890abcdef56\",\n  \"transaction_status\": \"PENDING\",\n  \"transaction_created_at\": \"2025-06-10T15:30:00.000000\",\n  \"paid_at\": \"2025-06-10T15:45:00\",\n  \"username\": \"johnny123\"\n}"}, {"table_id": "24c6c72e-1d65-4c0a-816f-bf411ee66887", "table_name": "TRANSFORMED.METRIC_TRANSACTIONS", "schema_id": "TRANSFORMED", "description": "This table contains the following fields: id, created_at, date, week, month, duration, sender_id, sender_country, receiver_id, receiver_country, corridor_pair, type, afriex_type, processor, amount, currency, fulfillment_asset_value, fulfillment_asset, amount_usd, state, is_signup_bonus, from_to_usd, to_to_usd. The METRIC_TRANSACTIONS table is designed to store detailed records of financial transactions, capturing essential metrics for analysis and reporting purposes.", "field_count": 23, "fields": [{"name": "id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each transaction."}, {"name": "created_at", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The timestamp when the transaction record was created."}, {"name": "date", "data_type": "DATE", "is_categorical": false, "is_datetime": true, "description": "The date when the transaction occurred."}, {"name": "week", "data_type": "DATE", "is_categorical": false, "is_datetime": true, "description": "The week number of the year when the transaction took place."}, {"name": "month", "data_type": "DATE", "is_categorical": false, "is_datetime": true, "description": "The month number when the transaction occurred."}, {"name": "duration", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": "The duration of the transaction process in seconds."}, {"name": "sender_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The unique identifier for the sender of the transaction."}, {"name": "sender_country", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The country from which the sender is located.", "categorical_values": ["AT", "BE", "CA", "CI", "CM", "CN", "CY", "DE", "EE", "EG", "ES", "ET", "FI", "FR", "GB", "GH", "GR", "HR", "IE", "IN", "IT", "KE", "LT", "LU", "LV", "MG", "MT", "NG", "NL", "PK", "PL", "PT", "RW", "SI", "SK", "SN", "TZ", "UG", "US", "ZA"]}, {"name": "receiver_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The unique identifier for the receiver of the transaction."}, {"name": "receiver_country", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The country where the receiver is located.", "categorical_values": ["AT", "BJ", "CA", "CI", "CM", "CN", "DE", "EG", "ES", "ET", "FR", "GB", "GH", "HT", "IE", "IN", "KE", "MG", "MW", "MZ", "NG", "NL", "PH", "PK", "RW", "SN", "TZ", "UG", "US", "ZA", "ZM"]}, {"name": "corridor_pair", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The pair of countries involved in the transaction corridor.", "categorical_values": ["CAD-->CAD", "CAD-->ETB", "CAD-->EUR", "CAD-->GBP", "CAD-->GHS", "CAD-->INR", "CAD-->KES", "CAD-->MWK", "CAD-->NGN", "CAD-->PKR", "CAD-->RWF", "CAD-->TZS", "CAD-->UGX", "CAD-->USD", "CAD-->XAF", "CAD-->XOF", "CAD-->ZAR", "CAD-->ZMW", "CNY-->ETB", "CNY-->NGN", "EGP-->EGP", "EGP-->ETB", "EGP-->KES", "EGP-->NGN", "ETB-->CAD", "ETB-->EGP", "ETB-->ETB", "ETB-->GBP", "ETB-->GHS", "ETB-->INR", "ETB-->KES", "ETB-->MGA", "ETB-->NGN", "ETB-->RWF", "ETB-->UGX", "ETB-->USD", "ETB-->XAF", "EUR-->CAD", "EUR-->ETB", "EUR-->EUR", "EUR-->GBP", "EUR-->GHS", "EUR-->KES", "EUR-->MZN", "EUR-->NGN", "EUR-->PKR", "EUR-->RWF", "EUR-->UGX", "EUR-->USD", "EUR-->XAF"]}, {"name": "type", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The type of transaction (e.g., transfer, payment).", "categorical_values": ["ADMIN_DEPOSIT", "ADMIN_REFERRAL", "ADMIN_WELCOME_BONUS", "ADMIN_WITHDRAW", "DEPOSIT", "SWAP", "USER TO USER", "WITHDRAWAL"]}, {"name": "afriex_type", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The specific type of transaction as defined by Afriex.", "categorical_values": ["ADMIN_DEPOSIT", "ADMIN_REFERRAL", "ADMIN_WELCOME_BONUS", "ADMIN_WITHDRAW", "DEPOSIT", "REFERRAL", "REVERSAL", "SWAP", "TRANSFER", "VIRTUAL_CARD_LOAD", "VIRTUAL_CARD_REFUND", "VIRTUAL_CARD_SPEND", "VIRTUAL_CARD_UNLOAD", "WITHDRAW"]}, {"name": "processor", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The payment processor used for the transaction.", "categorical_values": ["ALPAY", "BEYONIC", "BRIDGECARD", "CELLULANT", "CHAPA", "DLOCAL", "FAIRMONEY", "FINCRA", "FLICK", "HUB2", "JUICYWAY", "KORAPAY", "MONIEPOINT", "MONO", "ONAFRIQ", "PAYAZA", "PAYSTACK", "PROVIDUS", "STRIPE", "TERRAPAY", "TRICE", "UNKNOWN", "VFD", "WORLDPAY", "ZEEPAY", "ZENITH", "ZILMONEY"]}, {"name": "amount", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": "The amount of money involved in the transaction."}, {"name": "currency", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The currency in which the transaction amount is denominated.", "categorical_values": ["CAD", "CNY", "EGP", "ETB", "EUR", "GBP", "GHS", "INR", "KES", "MGA", "NGN", "PKR", "RWF", "TZS", "UGX", "USD", "XAF", "XOF", "ZAR"]}, {"name": "fulfillment_asset_value", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": "The value of the asset used to fulfill the transaction."}, {"name": "fulfillment_asset", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The type of asset used for fulfilling the transaction.", "categorical_values": ["CAD", "CNY", "EGP", "ETB", "EUR", "GBP", "GHS", "HTG", "INR", "KES", "MGA", "MWK", "MZN", "NGN", "PHP", "PKR", "RWF", "TZS", "UGX", "USD", "XAF", "XOF", "ZAR", "ZMW"]}, {"name": "amount_usd", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": "The equivalent amount of the transaction in USD."}, {"name": "state", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The current state of the transaction (e.g., completed, pending).", "categorical_values": ["CANCELLED", "DISPUTE_EVIDENCE_SUBMITTED", "DISPUTE_LOST", "DISPUTE_RESOLVED", "DISPUTE_WON", "FAILED", "IN_REVIEW", "PENDING", "PROCESSING", "REFUNDED", "RETRY", "SUCCESS", "UNCLEAR", "UNKNOWN"]}, {"name": "is_signup_bonus", "data_type": "BOOLEAN", "is_categorical": false, "is_datetime": false, "description": "Indicates whether the transaction includes a signup bonus."}, {"name": "from_to_usd", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": "The conversion rate from the transaction currency to USD at the time of the transaction."}, {"name": "to_to_usd", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": "The conversion rate to the transaction currency from USD."}], "sample_rows": "{\n  \"id\": \"a1b2c3d4e5f67890abcdef12\",\n  \"created_at\": \"2025-01-15T10:30:45.123456\",\n  \"date\": \"2025-01-15\",\n  \"week\": \"2025-01-12\",\n  \"month\": \"2025-01-01\",\n  \"duration\": 1234,\n  \"sender_id\": \"abcdef**********abcdef12\",\n  \"sender_country\": \"CA\",\n  \"receiver_id\": \"abcdef**********abcdef34\",\n  \"receiver_country\": \"GB\",\n  \"corridor_pair\": \"CAD-->GBP\",\n  \"type\": \"DEPOSIT\",\n  \"afriex_type\": \"DEPOSIT\",\n  \"processor\": \"PAYPAL\",\n  \"amount\": 150.75,\n  \"currency\": \"CAD\",\n  \"fulfillment_asset_value\": 300000.0,\n  \"fulfillment_asset\": \"GBP\",\n  \"amount_usd\": 112.50,\n  \"state\": \"PENDING\",\n  \"is_signup_bonus\": null,\n  \"from_to_usd\": null,\n  \"to_to_usd\": null\n}"}, {"table_id": "********-399c-407c-a754-72d15e45e3df", "table_name": "TRANSFORMED.USERS", "schema_id": "TRANSFORMED", "description": "This table contains the following fields: id, created_at, updated_at, username, country, email, phone, first_name, last_name, full_name, dob, referrer_id, kyc_status, kyc_updated_by, security_enabled, stripe_account_id, date, week, month, gender, device_os, phone_verified, tier_info, daily_limit, transaction_limit, account_username, account_referrer, kyc_verified_at, usernames, metadata. The USERS table is designed to store essential information about users of the application, including their personal details, account status, and verification information.", "field_count": 30, "fields": [{"name": "id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each user."}, {"name": "created_at", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The timestamp when the user account was created."}, {"name": "updated_at", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The timestamp when the user account was last updated."}, {"name": "username", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The unique username chosen by the user for login."}, {"name": "country", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The country where the user is located.", "categorical_values": ["AAA", "AE", "AF", "AG", "AI", "AL", "AM", "AO", "AR", "AT", "AU", "AZ", "BA", "BB", "BD", "BE", "BF", "BG", "BH", "BI", "BJ", "BM", "BO", "BR", "BS", "BW", "BY", "CA", "CD", "CF", "CG", "CH", "CI", "CL", "CM", "CN", "CO", "CR", "CU", "CV", "CW", "CY", "CZ", "DE", "DJ", "DK", "DM", "DO", "DZ", "EC"]}, {"name": "email", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The email address of the user."}, {"name": "phone", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The phone number of the user."}, {"name": "first_name", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The first name of the user."}, {"name": "last_name", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The last name of the user."}, {"name": "full_name", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The full name of the user, combining first and last names."}, {"name": "dob", "data_type": "DATE", "is_categorical": false, "is_datetime": true, "description": "The date of birth of the user."}, {"name": "referrer_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The ID of the user who referred this user."}, {"name": "kyc_status", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The Know Your Customer status of the user.", "categorical_values": ["failure", "notStarted", "pending", "rejected", "reuploadRequested", "reuploaded", "started", "submitted", "success"]}, {"name": "kyc_updated_by", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The ID of the user who last updated the KYC information."}, {"name": "security_enabled", "data_type": "BOOLEAN", "is_categorical": true, "is_datetime": false, "description": "Indicates if security features are enabled for the user.", "categorical_values": ["False", "True"]}, {"name": "stripe_account_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The ID of the user's Stripe account for payment processing."}, {"name": "date", "data_type": "DATE", "is_categorical": false, "is_datetime": true, "description": "The date associated with the user's account activities."}, {"name": "week", "data_type": "DATE", "is_categorical": false, "is_datetime": true, "description": "The week number of the year for the user's account activities."}, {"name": "month", "data_type": "DATE", "is_categorical": false, "is_datetime": true, "description": "The month number of the year for the user's account activities."}, {"name": "gender", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The gender of the user."}, {"name": "device_os", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The operating system of the device used by the user.", "categorical_values": ["ANDROID", "IOS"]}, {"name": "phone_verified", "data_type": "BOOLEAN", "is_categorical": true, "is_datetime": false, "description": "Indicates if the user's phone number has been verified.", "categorical_values": ["False", "True"]}, {"name": "tier_info", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "Information about the user's account tier or level."}, {"name": "daily_limit", "data_type": "NUMBER", "is_categorical": true, "is_datetime": false, "description": "The daily transaction limit for the user.", "categorical_values": ["1000.0", "3000.0", "5000.0", "7000.0", "10000.0", "30000.0", "500000.0"]}, {"name": "transaction_limit", "data_type": "NUMBER", "is_categorical": true, "is_datetime": false, "description": "The overall transaction limit for the user.", "categorical_values": ["400.0", "1000.0", "2000.0", "3000.0", "30000.0"]}, {"name": "account_username", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The username associated with the user's account."}, {"name": "account_referrer", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The referrer associated with the user's account."}, {"name": "kyc_verified_at", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The timestamp when the user's KYC was verified."}, {"name": "usernames", "data_type": "ARRAY", "is_categorical": false, "is_datetime": false, "description": "A list of alternative usernames associated with the user."}, {"name": "metadata", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "Additional metadata related to the user."}], "sample_rows": "{\n  \"id\": \"f3a2b4c5d6e7f8g9h0i1j2k3l\",\n  \"created_at\": \"2025-07-20T12:30:45.123000\",\n  \"updated_at\": \"2025-07-25T15:50:30.456000\",\n  \"username\": \"xzyab123\",\n  \"country\": \"CA\",\n  \"email\": \"<EMAIL>\",\n  \"phone\": \"+***********\",\n  \"first_name\": \"<PERSON>\",\n  \"last_name\": \"<PERSON><PERSON>\",\n  \"full_name\": \"<PERSON>\",\n  \"dob\": \"1990-05-15\",\n  \"referrer_id\": \"f3a2b4c5d6e7f8g9h0i1j2k4m\",\n  \"kyc_status\": \"pending\",\n  \"kyc_updated_by\": \"73eb9c92079641c3f6c75bd9\",\n  \"security_enabled\": true,\n  \"stripe_account_id\": \"acct_1XyZABCNoi98AOLH\",\n  \"date\": \"2025-07-20\",\n  \"week\": \"2025-07-18\",\n  \"month\": \"2025-07-01\",\n  \"gender\": null,\n  \"device_os\": \"ANDROID\",\n  \"phone_verified\": false,\n  \"tier_info\": \"{}\",\n  \"daily_limit\": 2500.0,\n  \"transaction_limit\": 1500.0,\n  \"account_username\": \"xzyab123\",\n  \"account_referrer\": \"randomreferrer\",\n  \"kyc_verified_at\": \"2025-07-21T10:00:00.000000\",\n  \"usernames\": \"[]\",\n  \"metadata\": \"{\\n  \\\"defaultPaymentMethodId\\\": {\\n    \\\"$oid\\\": \\\"f3a2b4c5d6e7f8g9h0i1j2k5n\\\"\\n  }\\n}\"\n}"}, {"table_id": "891c9df1-d5b6-4ee7-8da9-8fea77f12878", "table_name": "TRANSFORMED.TRANSACTIONS", "schema_id": "TRANSFORMED", "description": "", "field_count": 47, "fields": [{"name": "id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "created_at", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": ""}, {"name": "updated_at", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": ""}, {"name": "amount", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "currency", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "", "categorical_values": ["CAD", "CNY", "EGP", "ETB", "EUR", "GBP", "GHS", "INR", "KES", "MGA", "NGN", "PKR", "RWF", "TZS", "UGX", "USD", "XAF", "XOF", "ZAR"]}, {"name": "amount_usd", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "fulfillment_asset_value", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "fulfillment_asset", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "", "categorical_values": ["CAD", "CNY", "EGP", "ETB", "EUR", "GBP", "GHS", "HTG", "INR", "KES", "MGA", "MWK", "MZN", "NGN", "PHP", "PKR", "RWF", "TZS", "UGX", "USD", "XAF", "XOF", "ZAR", "ZMW"]}, {"name": "fulfillment_asset_value_usd", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "recipient_amt", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "recipient_currency", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "recipient_amount_usd", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "processor", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "", "categorical_values": ["ALPAY", "BEYONIC", "BRIDGECARD", "CELLULANT", "CHAPA", "DLOCAL", "FAIRMONEY", "FINCRA", "FLICK", "HUB2", "JUICYWAY", "KORAPAY", "MONIEPOINT", "MONO", "ONAFRIQ", "PAYAZA", "PAYSTACK", "PROVIDUS", "STRIPE", "TERRAPAY", "TRICE", "UNKNOWN", "VFD", "WORLDPAY", "ZEEPAY", "ZENITH", "ZILMONEY"]}, {"name": "transaction_type", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "", "categorical_values": ["ADMIN_DEPOSIT", "ADMIN_REFERRAL", "ADMIN_WELCOME_BONUS", "ADMIN_WITHDRAW", "DEPOSIT", "REFERRAL", "REVERSAL", "SWAP", "TRANSFER", "VIRTUAL_CARD_LOAD", "VIRTUAL_CARD_REFUND", "VIRTUAL_CARD_SPEND", "VIRTUAL_CARD_UNLOAD", "WITHDRAW"]}, {"name": "state", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "", "categorical_values": ["CANCELLED", "DISPUTE_EVIDENCE_SUBMITTED", "DISPUTE_LOST", "DISPUTE_RESOLVED", "DISPUTE_WON", "FAILED", "IN_REVIEW", "PENDING", "PROCESSING", "REFUNDED", "RETRY", "SUCCESS", "UNCLEAR", "UNKNOWN"]}, {"name": "user_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "sender_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "source_country", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "", "categorical_values": ["AT", "BE", "CA", "CI", "CM", "CN", "CY", "DE", "EE", "EG", "ES", "ET", "FI", "FR", "GB", "GH", "GR", "HR", "IE", "IN", "IT", "KE", "LT", "LU", "LV", "MG", "MT", "NG", "NL", "PK", "PL", "PT", "RW", "SI", "SK", "SN", "TZ", "UG", "US", "ZA"]}, {"name": "receiver_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "recipient_country", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "", "categorical_values": ["AT", "BJ", "CA", "CI", "CM", "CN", "DE", "EG", "ES", "ET", "FR", "GB", "GH", "HT", "IE", "IN", "KE", "MG", "MW", "MZ", "NG", "NL", "PH", "PK", "RW", "SN", "TZ", "UG", "US", "ZA", "ZM"]}, {"name": "transfer_ref", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "reference", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "bank_account_info", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "card_full_details", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "description", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "rates", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "payout_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "session_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "ledger_transaction_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "ledger_action_initiated_by", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "ledger_action_initiator_info", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "bank_account_deposit_details", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "transaction_status_description", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "reason_for_reversal", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "date", "data_type": "DATE", "is_categorical": false, "is_datetime": true, "description": ""}, {"name": "week", "data_type": "DATE", "is_categorical": false, "is_datetime": true, "description": ""}, {"name": "month", "data_type": "DATE", "is_categorical": false, "is_datetime": true, "description": ""}, {"name": "fee", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "otc_rate", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "processor_response_meta", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "promo_name", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "", "categorical_values": ["CHURNED_USER", "NEW_USER", "REGULATED_DEBIT", "UNREGULATED_DEBIT"]}, {"name": "promo_discount", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "tier_id", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "", "categorical_values": ["6516e5a6b5d8df9340ffac3b", "6516e5b4b5d8df9340ffac42", "66914c8bee6bfc188e7fba37", "669bac5798e8ad730201c38b", "66a0df9049d3c6d6b841fe01", "670567bb7949b84952d86117", "670567ee7949b84952d86219", "6705681d7949b84952d86230", "670568517949b84952d86244", "670568757949b84952d8624e", "67406a915b62d09b9813a94e", "67cc4545ce02680d78a20b24", "67cc473ace02680d78a20bd2", "67cc6bf4ce02680d78a23acc", "67cc715cce02680d78a24b67", "67cc7240ce02680d78a24cad", "67cc73f7ce02680d78a252b1", "67eed525f4a776c15583e8de", "67eed584f4a776c15583e9cc", "67eed5c4f4a776c15583e9dc", "67eed6a0f4a776c15583eb32", "67eed9dcf4a776c15583f1de", "67eeda77f4a776c15583f2c3", "67eedabbf4a776c15583f446", "67eedb14f4a776c15583f46a"]}, {"name": "deposit_transaction_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "channel", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "", "categorical_values": ["ACH_BANK_ACCOUNT", "ADMIN", "BANK_ACCOUNT", "CARD", "INTERAC", "INTERNAL", "MOBILE_MONEY", "RFP", "UPI", "VIRTUAL_BANK_ACCOUNT", "VIRTUAL_CARD", "WIDGET"]}, {"name": "from_to_usd", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "to_to_usd", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": ""}], "sample_rows": "{\n  \"id\": \"b3f1c2d4e5f6a7b8c9d0e1f2\",\n  \"created_at\": \"2024-11-29T12:45:30.123000\",\n  \"updated_at\": \"2024-12-13T08:15:45.678000\",\n  \"amount\": 512.0,\n  \"currency\": \"USD\",\n  \"amount_usd\": 512.0,\n  \"fulfillment_asset_value\": 1234567.89,\n  \"fulfillment_asset\": \"EUR\",\n  \"fulfillment_asset_value_usd\": null,\n  \"recipient_amt\": null,\n  \"recipient_currency\": null,\n  \"recipient_amount_usd\": null,\n  \"processor\": \"PAYSTACK\",\n  \"transaction_type\": \"DEPOSIT\",\n  \"state\": \"COMPLETED\",\n  \"user_id\": \"b3f1c2d4e5f6a7b8c9d0e1a2\",\n  \"sender_id\": null,\n  \"source_country\": \"US\",\n  \"receiver_id\": \"b3f1c2d4e5f6a7b8c9d0e1a2\",\n  \"recipient_country\": \"FR\",\n  \"transfer_ref\": \"Transfer-**********abcdef-**********\",\n  \"reference\": \"Transfer-**********abcdef-**********\",\n  \"bank_account_info\": \"{\\n  \\\"accountName\\\": \\\"JANE DOE\\\",\\n  \\\"accountNumber\\\": \\\"**********\\\",\\n  \\\"accountPhone\\\": \\\"\\\",\\n  \\\"bankCode\\\": \\\"000001\\\",\\n  \\\"bankName\\\": \\\"First Bank\\\",\\n  \\\"country\\\": \\\"FR\\\",\\n  \\\"currency\\\": \\\"EUR\\\",\\n  \\\"userAcknowledgedScamAlert\\\": false\\n}\",\n  \"card_full_details\": \"null\",\n  \"description\": null,\n  \"rates\": \"{\\n  \\\"USD\\\": {\\n    \\\"GBP\\\": \\\"0.75\\\",\\n    \\\"EUR\\\": \\\"0.85\\\",\\n    \\\"USD\\\": \\\"1\\\"\\n  },\\n  \\\"EUR\\\": {\\n    \\\"GBP\\\": \\\"0.88\\\",\\n    \\\"EUR\\\": \\\"1\\\",\\n    \\\"USD\\\": \\\"1.18\\\"\\n  },\\n  \\\"GBP\\\": {\\n    \\\"GBP\\\": \\\"1\\\",\\n    \\\"EUR\\\": \\\"1.14\\\",\\n    \\\"USD\\\": \\\"1.33\\\"\\n  }\\n}\",\n  \"payout_id\": \"b3f1c2d4e5f6a7b8c9d0e1b3\",\n  \"session_id\": \"******************************\",\n  \"ledger_transaction_id\": \"b3f1c2d4e5f6a7b8c9d0e1c4\",\n  \"ledger_action_initiated_by\": \"abcdef**********abcdef**********\",\n  \"ledger_action_initiator_info\": \"{\\n  \\\"email\\\": \\\"<EMAIL>\\\",\\n  \\\"name\\\": \\\"Example User\\\",\\n  \\\"phone\\\": \\\"+**********\\\"\\n}\",\n  \"bank_account_deposit_details\": null,\n  \"transaction_status_description\": null,\n  \"reason_for_reversal\": null,\n  \"date\": \"2024-11-29\",\n  \"week\": \"2024-11-25\",\n  \"month\": \"2024-11-01\",\n  \"fee\": 5.0,\n  \"otc_rate\": null,\n  \"processor_response_meta\": \"{\\n  \\\"fee\\\": 10,\\n  \\\"sessionId\\\": \\\"******************************\\\",\\n  \\\"statusMessage\\\": \\\"SUCCESS\\\",\\n  \\\"transactionReference\\\": \\\"XYZ**********abcdef**********\\\",\\n  \\\"transactionType\\\": \\\"E2E\\\"\\n}\",\n  \"promo_name\": \"WELCOME_OFFER\",\n  \"promo_discount\": 0.0150,\n  \"tier_id\": \"abcdef**********abcdef**********\",\n  \"deposit_transaction_id\": \"b3f1c2d4e5f6a7b8c9d0e1d5\",\n  \"channel\": \"CREDIT_CARD\",\n  \"from_to_usd\": null,\n  \"to_to_usd\": null\n}"}]}