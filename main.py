from app.create_app import create_app
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
from fastapi import Request, FastAPI, Depends, Form
import os
import uvicorn
from fastapi import APIRouter, HTTPException, BackgroundTasks,Request
from fastapi.responses import FileResponse, RedirectResponse
import os
from sql_copilot.handlers.feedback_manager import get_feedback_stats,save_feedback_and_execute_sql
from fastapi.templating import Jinja2Templates
from fastapi.responses import FileResponse, HTMLResponse
from fastapi.templating import Jinja2Templates
from pydantic import BaseModel,Field
from typing import Optional, Dict, Any, List
import pandas as pd
import json
import base64
from datetime import datetime, timedelta
import asyncio
import glob
import logging
from app.databse import test_db_connection, init_db

from app.core.session import is_authenticated, get_current_user
import sys
import redis.asyncio as aioredis
from fastapi import WebSocket, WebSocketDisconnect
from app.routes.routes import include_routers
# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = create_app()

# Jinja2 templates
templates = Jinja2Templates(directory="frontend")

REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379/0")

@app.on_event("startup")
def initialize_database():
    """Ensure all tables are created at startup."""
    init_db()

@app.on_event("startup")
def startup_event():
    if not test_db_connection():
        print("Database connection failed. Exiting.")
        sys.exit(1)

# HTML page routes
@app.get("/login", response_class=HTMLResponse)
def login_page(request: Request):
    return templates.TemplateResponse("login.html", {"request": request, "user": None})

@app.get("/signup", response_class=HTMLResponse)
def signup_page(request: Request):
    return templates.TemplateResponse("signup.html", {"request": request, "user": None})

@app.post("/signup", response_class=HTMLResponse)
async def signup_submit(request: Request):
    """Handle signup form submission"""
    try:
        form_data = await request.form()
        
        # Create user data
        user_data = {
            "full_name": form_data.get("name"),
            "email": form_data.get("email"),
            "password": form_data.get("password"),
            "organization_name": form_data.get("organization_name"),
            "organization_description": form_data.get("organization_description") or None
        }
        
        # Validate required fields
        if not all([user_data["full_name"], user_data["email"], user_data["password"], user_data["organization_name"]]):
            return templates.TemplateResponse("signup.html", {
                "request": request,
                "error": "All required fields must be filled",
                "user": None
            })
        
        # Import the auth router function directly
        from app.routes.auth import register, UserRegister
        from app.databse import get_db
        from sqlalchemy.orm import Session
        
        # Get database session
        db = next(get_db())
        
        # Create UserRegister object
        user_register = UserRegister(**user_data)
        
        # Call the register function
        result = register(user_register, db)

        # Fetch the newly created user from the database
        from app.models import User
        db_user = db.query(User).filter(User.email == user_data["email"]).first()
        if not db_user:
            return templates.TemplateResponse("signup.html", {
                "request": request,
                "error": "Registration failed. Please try again.",
                "user": None
            })

        # Log the user in by generating an access token
        from app.core.session import login_user
        access_token = login_user(request, db_user)

        # Redirect to dashboard and set the access_token cookie
        response = RedirectResponse(url="/dashboard", status_code=303)
        response.set_cookie(key="access_token", value=access_token, httponly=True, max_age=60*60*24)
        return response
        
    except Exception as e:
        logger.error(f"Signup error: {str(e)}")
        return templates.TemplateResponse("signup.html", {
            "request": request,
            "error": "Registration failed. Please try again.",
            "user": None
        })



@app.get("/dashboard", response_class=HTMLResponse)
def dashboard_page(request: Request):
    from app.core.session import get_current_user
    user = get_current_user(request)
    if not user:
        return RedirectResponse(url="/login", status_code=303)
    return templates.TemplateResponse("dashboard.html", {"request": request, "user": user})

@app.get("/connections", response_class=HTMLResponse)
def connections_page(request: Request):
    from app.core.session import get_current_user
    user = get_current_user(request)
    if not user:
        return RedirectResponse(url="/login", status_code=303)
    return templates.TemplateResponse("connections.html", {"request": request, "user": user})

# async def cleanup_old_temp_files():
#     """Clean up CSV files older than 3 days from the temp_files directory"""
#     try:
#         temp_dir = "temp_files"
#         if not os.path.exists(temp_dir):
#             return

#         # Calculate the cutoff time (3 days ago)
#         cutoff_time = datetime.now() - timedelta(days=3)
        
#         # Get all CSV files in the temp_files directory
#         csv_files = glob.glob(os.path.join(temp_dir, "query_results_*.csv"))
        
#         for file_path in csv_files:
#             try:
#                 # Get file's last modification time
#                 file_mtime = datetime.fromtimestamp(os.path.getmtime(file_path))
                
#                 # If file is older than 3 days, delete it
#                 if file_mtime < cutoff_time:
#                     os.remove(file_path)
#                     logger.info(f"Deleted old file: {file_path}")
#             except Exception as e:
#                 logger.error(f"Error processing file {file_path}: {str(e)}")
                
#     except Exception as e:
#         logger.error(f"Error in cleanup_old_temp_files: {str(e)}")


# async def schedule_cleanup():
#     """Schedule the cleanup task to run every weekend"""
#     while True:
#         now = datetime.now()
#         # Check if it's weekend (Saturday or Sunday)
#         if now.weekday() >= 5:  # 5 is Saturday, 6 is Sunday
#             await cleanup_old_temp_files()
#             # Sleep until next day
#             await asyncio.sleep(24 * 60 * 60)
#         else:
#             # Sleep until next day
#             await asyncio.sleep(24 * 60 * 60)

# @app.on_event("startup")
# async def startup_event():
#     """Start the cleanup scheduler when the application starts"""
#     asyncio.create_task(schedule_cleanup())


# app.mount("/static", StaticFiles(directory="static"), name="static")  
app.mount("/static", StaticFiles(directory="frontend/static"), name="static")

@app.get("/", response_class=HTMLResponse)
async def get_home_page(request: Request):
    """Home page - shows dashboard if logged in, otherwise login page"""
    # Check if user is authenticated
    if is_authenticated(request):
        # User is logged in, show dashboard
        return templates.TemplateResponse("dashboard.html", {"request": request})
    else:
        # User is not logged in, show login page
        return templates.TemplateResponse("login.html", {"request": request})


@app.get("/feedback", response_class=HTMLResponse)
async def get_feedback(request: Request):
    """Endpoint to retrieve all feedback and render the HTML page"""
    try:
        feedback_data = await get_feedback_stats()  # Ensure this is awaited
        current_date = datetime.now().strftime("%Y-%m-%d")
        current_month = datetime.now().strftime("%B")  # Get the current month
        current_day = datetime.now().strftime("%A")  # Get the current day
        
        # Calculate the current week of the current month
        current_week_of_month = (datetime.now().day - 1) // 7 + 1  # Week number in the current month
        
        return templates.TemplateResponse(
            "feedback.html",
            {
                "request": request,
                "total_correct": feedback_data['total_correct'],
                "total_wrong": feedback_data['total_wrong'],
                "weekly_correct": feedback_data['weekly_correct'],
                "weekly_wrong": feedback_data['weekly_wrong'],
                "all_feedback": feedback_data["all_feedback"],
                "current_date": current_date,
                "current_week": current_week_of_month,
                "current_month": current_month,  # Pass current month to the template
                "current_day": current_day,  # Pass current day to the template

            }
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving feedback: {str(e)}")

@app.get("/health")
async def health_check():
    return {"status": "ok"}

# @app.get("/training", response_class=HTMLResponse)
# async def get_training_examples_page(request: Request):
#         """Serve the training examples HTML page."""
#         return templates.TemplateResponse("training_examples.html", {"request": request})

@app.get("/settings", response_class=HTMLResponse)
def settings_page(request: Request):
    from app.core.session import get_current_user
    user = get_current_user(request)
    if not user:
        return RedirectResponse(url="/login", status_code=303)
    return templates.TemplateResponse("settings.html", {"request": request, "user": user})

@app.post("/settings", response_class=HTMLResponse)
async def update_settings(request: Request):
    from app.core.session import get_current_user
    from app.databse import get_db
    from app.models import User
    db = next(get_db())
    user = get_current_user(request)
    if not user:
        return RedirectResponse(url="/login", status_code=303)
    form = await request.form()
    new_email = form.get("email")
    new_org_name = form.get("organization_name")
    new_org_desc = form.get("organization_description")
    try:
        # Check if email is changing and not already taken
        if new_email != user.email:
            existing = db.query(User).filter(User.email == new_email).first()
            if existing:
                return templates.TemplateResponse("settings.html", {"request": request, "user": user, "error": "Email already in use."})
            user.email = new_email
        user.organization_name = new_org_name
        user.organization_description = new_org_desc
        db.commit()
        db.refresh(user)
        return templates.TemplateResponse("settings.html", {"request": request, "user": user, "success": "Profile updated successfully."})
    except Exception as e:
        db.rollback()
        return templates.TemplateResponse("settings.html", {"request": request, "user": user, "error": f"Update failed: {str(e)}"})

# WebSocket endpoint for schema generation progress
@app.websocket("/ws/schema-progress/{request_id}")
async def websocket_schema_progress(websocket: WebSocket, request_id: str):
    await websocket.accept()
    redis = await aioredis.from_url(REDIS_URL)
    channel = f"schema_progress:{request_id}"
    pubsub = redis.pubsub()
    await pubsub.subscribe(channel)
    try:
        while True:
            message = await pubsub.get_message(ignore_subscribe_messages=True, timeout=5)
            if message and message["type"] == "message":
                await websocket.send_text(message["data"].decode("utf-8"))
            else:
                # Send heartbeat every 5 seconds if no message
                await websocket.send_text('{"type": "heartbeat"}')
            await asyncio.sleep(1)
    except WebSocketDisconnect:
        pass
    finally:
        await pubsub.unsubscribe(channel)
        await pubsub.close()
        await redis.close()



@app.get("/connection_view", response_class=HTMLResponse)
def connection_view_page(request: Request):
    from app.core.session import get_current_user
    user = get_current_user(request)
    if not user:
        return RedirectResponse(url="/login", status_code=303)
    return templates.TemplateResponse("connection_view.html", {"request": request, "user": user})

if __name__ == "__main__":
    uvicorn.run("main:app", host="0.0.0.0", port=5045)




'''
gunicorn main:app \
  --worker-class uvicorn.workers.UvicornWorker \
  --workers 4 \
  --bind 0.0.0.0:5045 \
  --log-level info \
  --access-logfile - \
  --error-logfile - \
  --timeout 300 \
  --keep-alive 5 \
  --graceful-timeout 300
  
'''



