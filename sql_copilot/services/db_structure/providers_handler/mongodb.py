import json
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
from enum import Enum
import uuid
from sql_copilot.services.db_structure.transformer import BaseSchemaTransformer
from sql_copilot.services.db_structure.providers import DatabaseProvider

class MongoDBSchemaTransformer(BaseSchemaTransformer):
    """Transformer for MongoDB database schemas"""
    
    def __init__(self):
        super().__init__(DatabaseProvider.MONGODB)
    
    def validate_schema_data(self, raw_schema_data: Dict[str, Any]) -> bool:
        """Validate MongoDB schema data format"""
        # Check if collections key exists and is a list
        if "collections" not in raw_schema_data:
            return False
        
        required_fields = ["database_name", "collection_name"]
        
        for collection_info in raw_schema_data["collections"]:
            if not all(field in collection_info for field in required_fields):
                return False
        
        return True
    
    def transform_to_json_db(self, raw_schema_data: Dict[str, Any]) -> Dict[str, Any]:
        """Transform MongoDB schema data to JSON database format"""
        if not self.validate_schema_data(raw_schema_data):
            raise ValueError("Invalid MongoDB schema data format")
        
        collections_data = raw_schema_data["collections"]
        
        # Group collections by database
        databases_dict = {}
        
        for collection_info in collections_data:
            db_name = collection_info["database_name"]
            
            if db_name not in databases_dict:
                databases_dict[db_name] = {"collections": []}
            
            databases_dict[db_name]["collections"].append(collection_info)
        
        db_structure = {
            "provider": self.provider_name,
            "schemas": [],  # databases in MongoDB context
            "tables": []    # collections in MongoDB context (original data preserved)
        }
        
        # Create database records (equivalent to schemas) - Table Overview Node
        for db_name, db_data in databases_dict.items():
            collection_descriptions = []
            active_collections = 0
            
            for collection in db_data["collections"]:
                # Only process active collections for overview
                if collection.get("status") == "deactivated":
                    continue
                    
                active_collections += 1
                categorical_examples = []
                
                for field in collection.get("fields", []):
                    if field.get("is_categorical") and field.get("categorical_values"):
                        categorical_examples.append({
                            "field_name": field["name"],
                            "sample_values": field["categorical_values"][:5]
                        })
                
                collection_overview = {
                    "collection_name": collection["collection_name"],
                    "description": collection.get("description", ""),
                    "field_count": len(collection.get("fields", [])),
                    "has_sample_documents": bool(collection.get("sample_documents")),
                    "status": collection.get("status", "active"),
                    "categorical_examples": categorical_examples
                }
                collection_descriptions.append(collection_overview)
            
            schema_record = {
                "schema_id": db_name,
                "schema_name": db_name,
                "description": f"MongoDB database containing {len(db_data['collections'])} collection(s), {active_collections} active",
                "total_collections": len(db_data["collections"]),
                "active_collections": active_collections,
                "collections_overview": collection_descriptions
            }
            
            db_structure["schemas"].append(schema_record)
        
        # Create collection records - Original Data Node (preserving exact structure)
        for collection_info in collections_data:
            # Keep the original structure exactly as provided
            table_record = {
                "table_id": str(uuid.uuid4()),
                "schema_id": collection_info["database_name"],  # Foreign key
                # Preserve original MongoDB structure
                "database_name": collection_info["database_name"],
                "collection_name": collection_info["collection_name"],
                "description": collection_info.get("description", ""),
                "fields": collection_info.get("fields", []),
                "sample_documents": collection_info.get("sample_documents", []),
                "status": collection_info.get("status", "active")
            }
            
            db_structure["tables"].append(table_record)
        
        return db_structure