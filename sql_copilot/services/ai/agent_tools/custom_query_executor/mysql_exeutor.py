import mysql.connector
from typing import Optional, Tuple, List, Any

def execute_query_mysql(
    query: str,
    connection_params: dict,
    schema: Optional[str] = None
) -> Tuple[Optional[List[Any]], Optional[str]]:
    """
    Execute SQL query on MySQL database and return results.

    Args:
        query (str): SQL query to execute
        connection_params (dict): MySQL connection parameters
        schema (Optional[str]): Optional schema/database name

    Returns:
        Tuple[Optional[List[Any]], Optional[str]]: 
            - Query results (list of dicts) if successful, else None
            - Error message if failed, else None
    """
    print(f"Executing MySQL query: {query}")

    conn = None
    cursor = None

    try:
        conn_params = connection_params.copy()
        if schema:
            conn_params['database'] = schema

        conn = mysql.connector.connect(**conn_params)
        cursor = conn.cursor(dictionary=True)

        cursor.execute(query)
        results = cursor.fetchall()
        results_list = [dict(row) for row in results]

        print(f"Query executed successfully. Fetched {len(results_list)} rows.")
        return results_list, None

    except Exception as e:
        error_msg = f"MySQL query execution failed: {str(e)}"
        print(error_msg)
        return None, error_msg

    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()
