from pymongo import MongoClient
from pymongo.command_cursor import CommandCursor
from typing import Any, Union, List, Tuple, Dict


def construct_mongo_connection_string(
    username: str,
    password: str,
    cluster_url: str,
    database: str,
    options: str = "retryWrites=true&w=majority"
) -> str:
    return f"mongodb+srv://{username}:{password}@{cluster_url}/{database}?{options}"


def execute_query_mongodb(
    connection_params: Dict[str, str],
    collection_name: str,
    query: Dict[str, Any]
) -> Tuple[Union[List[Any], None], Union[str, None]]:
    """
    Executes a MongoDB find query on a given collection.

    Args:
        connection_params (dict): MongoDB connection info.
        collection_name (str): Name of the collection to query.
        query (dict): MongoDB filter query.

    Returns:
        Tuple[results, error]: 
            - results: List of documents if successful, else None
            - error: Error string if failed, else None
    """
    try:
        connection_string = construct_mongo_connection_string(
            connection_params['username'],
            connection_params['password'],
            connection_params['cluster_url'],
            connection_params['database']
        )
        
        print(f"Connecting to MongoDB with: {connection_string}")
        client = MongoClient(connection_string)
        db = client[connection_params['database']]
        collection = db[collection_name]

        print(f"Running query on collection '{collection_name}': {query}")
        cursor = collection.find(query)
        results = list(cursor)
        print(f"Fetched {len(results)} documents.")
        return results, None

    except Exception as e:
        error_msg = f"MongoDB query failed: {str(e)}"
        print(error_msg)
        return None, error_msg
