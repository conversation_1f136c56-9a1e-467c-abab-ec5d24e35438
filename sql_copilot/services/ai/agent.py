import requests
import json
from langchain_openai import Chat<PERSON>penA<PERSON>
from langchain_anthropic import ChatAnthropic
import os
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
from typing_extensions import TypedDict,Literal
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langgraph.graph import StateGraph, START, END
from langgraph.checkpoint.memory import MemorySaver
from langchain_core.messages import HumanMessage, AIMessage

from dataclasses import dataclass
from dotenv import load_dotenv
from langchain_core.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from .prompts.user_engager import get_user_engagement_prompt_v2,get_user_engagement_prompt_v3
from .prompts.responder import response_formulation_prompt
from .prompts.table_filter import table_filter_prompt_v2
from .prompts.query_generator.mongodb import mongo_query_guidance
from .utils import get_filtered_db,get_database_version_info
from .core import AgentState
from .agent_tools.custom_query_executor import QUERY_EXECUTORS
from .prompts.query_generator import QUERY_GENERATOR
from config import Config
from .config.models import ModelConfig
from sql_copilot.services.db_structure.transformer import SchemaTransformerFactory, DatabaseProvider
import random


@dataclass
class Message:
    role: str  # 'human' or 'ai'
    content: str

class UserEngagnerOutput(BaseModel):
    generate_query: bool
    interpreted_question: str
    status: str
    engager_question: str

USER_ENGAGER_TEMPERATURE = ModelConfig.USER_ENGAGER_TEMPERATURE
USER_ENGAGER_MODEL = ModelConfig.USER_ENGAGER_MODEL



class TableFilterOutput(BaseModel):  # Fixed typo from 'TableFilterOuput' to 'TableFilterOutput'
    tables: List[str]

TABLE_FILTER_TEMPERATURE = ModelConfig.TABLE_FILTER_TEMPERATURE
TABLE_FILTER_MODEL = ModelConfig.TABLE_FILTER_MODEL


class QueryGeneratorOutput(BaseModel):
    executed_query: str
    mode: str
    has_analytics: bool
    plot_code: str
    filemodecols: List[str]
    download_name: str  # Make download_name optional with empty string default



QUERY_GENERATOR_TEMPERATURE = ModelConfig.QUERY_GENERATOR_TEMPERATURE
QUERY_GENERATOR_MODEL =  ModelConfig.QUERY_GENERATOR_MODEL
#QUERY_GENERATOR_MODEL="gpt-4o"

class ResponderOutput(BaseModel):  # Fixed typo from 'Responderoutput' to 'ResponderOutput'
    message: str


RESPONDER_TEMPERATURE = ModelConfig.RESPONDER_TEMPERATURE # Fixed typo from 'RESPONDER_EMPERATURE' to 'RESPONDER_TEMPERATURE'
RESPONDER_MODEL = ModelConfig.RESPONDER_MODEL

claude_api_key = Config.ANTHROPIC_API_KEY_2

class SqlCopilot:
    def __init__(self,provider:str,connection_params:dict,database_structure: List[Dict], training_examples: List[Dict]=None,context: Dict=None, 
                 history: Optional[List[Message]] = None, query: Optional[str] = None,db_type="sql",no_sql_sample=None,connection_id=None,company_name=None):
        self.training_examples = training_examples
        self.database_structure = database_structure
        self.context = context
        self.history = history if history is not None else []
        self.query = query
        self.db_type = db_type
        self.provider = provider
        self.connection_params = connection_params
        self.no_sql_sample = no_sql_sample
        self.connection_id = connection_id
        self.company_name = company_name
        self.max_retry = 1  # Maximum retry attempts
        self.model_config = ModelConfig()  # Initialize ModelConfig instance

        if self.db_type == "no_sql":
             self.no_sql_sample = mongo_query_guidance

        print("SqlCopilot initialized with training examples and database structure.")

    def user_engager(self,state):
        print("Entering user_engager method.")
        prompt = ChatPromptTemplate.from_messages([
            ("system", get_user_engagement_prompt_v3()),
            MessagesPlaceholder(variable_name="messages")
        ])
    
        #model = ChatOpenAI(model=USER_ENGAGER_MODEL, temperature=USER_ENGAGER_TEMPERATURE)
        model = ChatAnthropic(api_key=claude_api_key,model=self.model_config.USER_ENGAGER_MODEL, temperature=USER_ENGAGER_TEMPERATURE)
        model = model.with_structured_output(UserEngagnerOutput)
        prompt = prompt.invoke({
            "messages": state["messages"],
            "CONTEXT": self.context,
            "database_structure": self.database_structure["schemas"],  # Get database structure from state
            "training_examples":self.training_examples,
            'provider':self.provider,
            "company_name":self.company_name
        })
    
        response = model.invoke(prompt)
        print("User engagement response received.")

        print(f"USEER ENEGAGER RESPONSE : {response}")

        return {
            "generate_query": response.generate_query,
            "interpreted_question": response.interpreted_question,
            "status": response.status,
            "engager_question": response.engager_question,
            "retry_count": state.get("retry_count", 0)  # Initialize retry count
        }
    

    def tables_filter(self,state):
        print("Entering tables_filter method.")
        prompt = ChatPromptTemplate.from_messages([
                ("system", table_filter_prompt_v2()),
                MessagesPlaceholder(variable_name="messages")
            ])
        
        #model = ChatOpenAI(model=TABLE_FILTER_MODEL, temperature=TABLE_FILTER_TEMPERATURE)
        models =   ["claude-sonnet-4-20250514", "claude-3-5-sonnet-latest", "claude-3-7-sonnet-latest"]
        model = random.choice(models)
        print(f"TABLE_FILTER_MODEL: {model}")
        model = ChatAnthropic(api_key=claude_api_key, model=model, temperature=QUERY_GENERATOR_TEMPERATURE)
        model = model.with_structured_output(TableFilterOutput)  # Fixed typo here as well
        prompt = prompt.invoke({
                    "messages": state["messages"],
                    "CONTEXT": self.context,
                    "database_structure": self.database_structure["schemas"],  # Get database structure from state
                    "interpreted_question": state["interpreted_question"]
                })
        
        response = model.invoke(prompt)
        print(f"Tables filtered response received.: {response.tables}")

        return {"filtered_tables": response.tables}
    
    def query_generator(self,state):
        print("Entering query_generator method.")
        database_structure = get_filtered_db(state,self.database_structure["tables"],type=self.db_type)
        
        # Get current retry count and increment if this is a retry
        current_retry_count = state.get("retry_count", 0)
        is_retry = current_retry_count > 0 or state.get("execution_error") is not None
        
        if is_retry:
            current_retry_count += 1
            print(f"RETRY MODE: Attempt {current_retry_count}/{self.max_retry}")
        
        kwargs = None
        version_info = get_database_version_info(self.provider, self.connection_params, QUERY_EXECUTORS)
        print(f"THIS IS VERSION RESULT: {version_info}")
     
        kwargs = f"""
            {version_info}
    """
            
        # Enhanced prompt for retry scenarios
        base_prompt = QUERY_GENERATOR[self.provider]()
        
        # Add retry-specific instructions if this is a retry attempt
        if is_retry:
            retry_instructions = """
            
                    🔄 **QUERY RETRY MODE ACTIVATED** 🔄

                    The previous query encountered an execution error. Please carefully analyze the error and generate a corrected query.

                    **Previous Query That Failed:**
                    {previous_query}

                    **Execution Error Encountered:**
                    {execution_error}

                    **RETRY INSTRUCTIONS:**
                    1. Carefully analyze the execution error message
                    2. Identify the specific issue (syntax, missing columns, wrong table names, etc.)
                    3. Generate a corrected query that addresses the error
                    4. Ensure the new query follows all syntax rules for {provider}
                    5. Double-check column names, table names, and data types against the database structure
                    6. Validate that all referenced fields exist in the provided database schema

                    This is retry attempt #{retry_count} of {max_retry}. Make sure the corrected query works properly.
            """.format(
                previous_query=state.get("generated_query", ""),
                execution_error=state.get("execution_error", ""),
                provider=self.provider,
                retry_count=current_retry_count,
                max_retry=self.max_retry
            )
            enhanced_prompt = base_prompt + retry_instructions
        else:
            enhanced_prompt = base_prompt
        
        prompt = ChatPromptTemplate.from_messages([
            ("system", enhanced_prompt),
            MessagesPlaceholder(variable_name="messages")
        ])

        models =   ["claude-sonnet-4-20250514", "claude-3-5-sonnet-latest", "claude-3-7-sonnet-latest"]
        model = random.choice(models)
       
        print(f"QUERY GENERATOR MODEL : {model}")
        model = ChatAnthropic(api_key=claude_api_key, model=model, temperature=QUERY_GENERATOR_TEMPERATURE, max_tokens_to_sample=4096)
        model = model.with_structured_output(QueryGeneratorOutput)
        
        prompt = prompt.invoke({
            "messages": state["messages"],
            "CONTEXT": self.context,
            "database_structure": database_structure,
            "training_examples": self.training_examples,
            "user_question": state["interpreted_question"],
            "no_sql_sample":self.no_sql_sample,
            "company_name":self.company_name,
            "provider":self.provider,
            "other_db_info":kwargs

        })
        
        response = model.invoke(prompt)
        print(f"Query generated. Retry count will be: {current_retry_count}")
        #print download name
        
        return {
            "executed_query": response.executed_query,
            "mode": response.mode,
            "has_analytics": response.has_analytics,
            "plot_code": response.plot_code,
            "filemodecols": response.filemodecols,
            "retry_count": current_retry_count ,
             "download_name": response.download_name # Update retry count here
        }
  
    def query_executor(self,state):
        print("Entering query_executor method.")
        """
        Execute SQL query with AI-powered retry on failure
        Returns: (data, error_message, final_query)
        """

        current_query = state["executed_query"]
        error_message = None
        data = None
        retry_count = state.get("retry_count", 0)

       
        
        print(f"Executing query (attempt {retry_count + 1}): {current_query[:100]}...")
        
        try:
            data,error = QUERY_EXECUTORS[self.provider](query=current_query,connection_params=self.connection_params)
            print("Query executed successfully.")

        except Exception as error:
            error_message = str(error)
            print(f"Error executing query: {error_message}")
        
        #print(f"THIS IS DATA: {data}")
        return {
            "data": data, 
            "execution_error": error_message,
            "retry_count": retry_count  # Preserve retry count
        }

    def responder(self,state):
        print("Entering responder method.")
        if state["mode"] == "file":
            data = None
        
        else:
            data = state["data"]
        prompt = ChatPromptTemplate.from_messages([
                ("system", response_formulation_prompt()),
                MessagesPlaceholder(variable_name="messages")
            ])
    
        
        model = ChatOpenAI(model=RESPONDER_MODEL, temperature=RESPONDER_TEMPERATURE)  # Fixed typo here
        model = model.with_structured_output(ResponderOutput)  # Fixed typo here
        prompt = prompt.invoke({
            "messages": state["messages"],
                    "interpreted_question":state["interpreted_question"],
                    "data":data,
                    "execution_error":state["execution_error"],
                        "mode":state["mode"],
                })
        
        response = model.invoke(prompt)
        print("Responder response generated.")

        return {
            "final_response": response.message
        }

    def create_workflow(self) -> StateGraph:
        print("Creating workflow.")
        workflow = StateGraph(state_schema=AgentState)
        
        # Add all nodes
        workflow.add_edge(START, "user_engager")
        workflow.add_node("user_engager", self.user_engager)
        workflow.add_node("query_generator", self.query_generator)  
        workflow.add_node("query_executor", self.query_executor)  
        workflow.add_node("responder", self.responder)  
        workflow.add_node("tables_filter", self.tables_filter)  

        # Define standard edges
        workflow.add_edge("tables_filter","query_generator")
        workflow.add_edge("responder", END)

        # Add conditional edges
        workflow.add_conditional_edges(
            "user_engager",
            self.conditional_edge_responder
        )
        
        # Add conditional edge for query executor (retry logic)
        workflow.add_conditional_edges(
            "query_executor",
            self.conditional_edge_executor
        )
        
        # Add conditional edge for query generator (after retry)
        workflow.add_conditional_edges(
            "query_generator",
            self.conditional_edge_query_generator
        )

        memory = MemorySaver()
        app = workflow.compile(checkpointer=memory)
        print("Workflow created successfully.")
        return app
    
    def conditional_edge_responder(self,state: AgentState) -> Literal[END, "tables_filter"]:
        if not state["generate_query"]:
            return END
        else:
            return "tables_filter"
    
    def conditional_edge_executor(self, state: AgentState) -> Literal["responder", "query_generator"]:
        """
        Determine the next step after query execution.
        If there's an execution error and retry count is less than max_retry, go back to query_generator.
        Otherwise, proceed to responder.
        """
        execution_error = state.get("execution_error")
        retry_count = state.get("retry_count", 0)
        
        print(f"Conditional edge executor - Error: {bool(execution_error)}, Retry count: {retry_count}/{self.max_retry}")
        
        if execution_error and retry_count < self.max_retry:
            print(f"Execution error detected. Will retry. Current count: {retry_count}")
            return "query_generator"
        else:
            if execution_error:
                print(f"Max retries ({self.max_retry}) reached. Proceeding to responder with error.")
            else:
                print("Query executed successfully. Proceeding to responder.")
            return "responder"
    
    def conditional_edge_query_generator(self, state: AgentState) -> Literal["query_executor"]:
        """
        After query generation (including retries), always go to executor.
        """
        return "query_executor"

    def convert_to_langchain_messages(self,messages: List[Message]) -> List[HumanMessage | AIMessage]:
        """Convert our Message objects to LangChain message objects"""
        return [
            HumanMessage(content=msg["message"]) if msg["role"] == "user" else AIMessage(content=msg["message"])
            for msg in messages
        ]
    
    def invoke(self) -> Any:
        print("Invoking the workflow.")

        conversation_id = "mr_random"
        config = {"configurable": {"thread_id": conversation_id}}
        app = self.create_workflow()
        
        # Prepare input messages
        if not self.history:
            # New conversation
            input_messages = [HumanMessage(content=self.query)] if self.query else [HumanMessage(content="Let's get started")]
        else:
            # Existing conversation
            history = self.convert_to_langchain_messages(self.history)
            input_messages = history + [HumanMessage(content=self.query)] if self.query else history
        
        # Initialize state with retry count
        initial_state = {
            "messages": input_messages,
            "retry_count": 0
        }

        output = app.invoke(initial_state, config)
        print("Workflow invoked successfully.")
        return output