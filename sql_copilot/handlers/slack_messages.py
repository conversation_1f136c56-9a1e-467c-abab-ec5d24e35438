import requests
from dotenv import load_dotenv
import os
import re
from slack_sdk import WebClient
import os
import json
import asyncio
import logging
from typing import List, Dict
import snowflake.connector
import os
from dotenv import load_dotenv
import datetime

import json

import requests
from dotenv import load_dotenv
import os
import re
from slack_sdk import WebClient
from config import Config
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


# Load Slack tokens

SLACK_BOT_TOKEN = Config.SLACK_BOT_TOKEN
SLACK_APP_TOKEN = Config.SLACK_APP_TOKEN




client = WebClient(token=SLACK_BOT_TOKEN)


# Get bot's own ID once at startup
try:
    bot_info = client.auth_test()
    BOT_USER_ID = bot_info["user_id"]
    print(f"Bot ID: {BOT_USER_ID}")
except Exception as e:
    print(f"Error getting bot ID: {e}")
    BOT_USER_ID = None


def get_user_name(user_id):
    """
    Fetch user name from Slack API
    Returns display name if available, otherwise real name, otherwise user ID
    """
    try:
        # Use the WebClient to get user info
        user_info = client.users_info(user=user_id)
        if user_info["ok"]:
            user = user_info["user"]
            # Try to get display name first, then real name, then fallback to user ID
            display_name = user.get("profile", {}).get("display_name", "")
            real_name = user.get("real_name", "")
            
            if display_name:
                return display_name
            elif real_name:
                return real_name
            else:
                return user_id
        else:
            logger.warning(f"Failed to get user info for {user_id}: {user_info.get('error')}")
            return "NOT PROVIDED"
    except Exception as e:
        logger.error(f"Error fetching user name for {user_id}: {e}")
        return "NOT PROVIDED"


def fetch_user_questions(channel, ts, k=10):
    """
    Fetches the conversation history from a Slack thread, including both user messages and bot responses.
    If a bot message contains an SQL query in its blocks, the query is appended to the bot's response.
    Now includes actual user names for better context.
    """
    if not BOT_USER_ID:
        raise ValueError("Bot user ID not found. Cannot fetch messages.")

    # Fetch thread messages
    url = "https://slack.com/api/conversations.replies"
    headers = {"Authorization": f"Bearer {SLACK_BOT_TOKEN}", "Content-Type": "application/x-www-form-urlencoded"}
    params = {"channel": channel, "ts": ts}
    response = requests.get(url, headers=headers, params=params)

    if response.status_code != 200:
        raise Exception(f"Slack API request failed: {response.status_code}")

    data = response.json()

    print(f"CONVERSATION DATA: {data}")
    if not data.get("ok"):
        raise Exception(f"Error fetching messages: {data.get('error')}")


    message_history = []
    previous_message = None

    # SQL query pattern
    sql_pattern = re.compile(r"\b(SELECT|INSERT|UPDATE|DELETE|FROM|WHERE|JOIN)\b", re.IGNORECASE)

    for msg in data.get("messages", []):
        user_id = msg.get("user", "Unknown")
        role = "assistant" if user_id == BOT_USER_ID else "user"

        print(f"MESSAGE: {msg}")

        text = msg.get("text", "").strip()
        sql_queries = []

        # Only search for SQL queries inside blocks if the message is from the bot
        if role == "assistant" and "blocks" in msg:
            for block in msg["blocks"]:
                if block.get("type") == "section" and "text" in block:
                    block_text = block["text"]["text"]
                    if sql_pattern.search(block_text):
                        sql_queries.append(block_text)

        if role == "user" and f"<@{BOT_USER_ID}>" in text:
            clean_text = re.sub(f"<@{BOT_USER_ID}>", "", text).strip()
            user_name = get_user_name(user_id)

            clean_text += f"\n\n(From (NAME OF USER): {user_name})"
            print(f"USER NAME: {user_name}")
            message_history.append({
                "role": role, 
                
                "message": clean_text
            })
        elif role == "assistant":
            new_message = {
                "role": role,   # You could also get the bot's name if needed
                "message": text
            }

            # Append any found SQL queries
            for sql_query in sql_queries:
                new_message["message"] += f"\n\n```sql\n{sql_query}\n```"

            message_history.append(new_message)
            previous_message = new_message  # Track last bot message

    print(f"convo history:{message_history}")
    return {"platform": "SLACK", "message_history": message_history[-k:]}