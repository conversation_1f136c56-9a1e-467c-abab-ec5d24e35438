{"provider": "snowflake", "schemas": [{"schema_id": "bc32920a-2bcd-4c8b-92d9-7f951b494f43", "schema_name": "TRANSFORMED", "description": "Snowflake schema containing 13 table(s)", "table_count": 13, "tables_overview": [{"table_name": "TRANSFORMED.AUTOMATE_RATE_TIER_DISCOUNT_RULES", "description": "This table contains the following fields: created_at, code, from_asset, to_asset, company_list, percent_higher_than_best, created_by, updated_by, updated_at. The purpose of this table is to define discount rules for rate tiers in an automated system, specifying the conditions under which discounts apply based on asset types and company criteria."}, {"table_name": "TRANSFORMED.AUTOMATE_RATE_TIER_DISCOUNT_LOG", "description": "This table contains the following fields: timestamp, code, from_asset, to_asset, discount, admin_discount, rule_by. The purpose of this table is to log discount tiers applied to asset exchanges, capturing relevant details about each discount event for auditing and analysis purposes."}, {"table_name": "TRANSFORMED.MEDICI_BALANCES", "description": "This table contains the following fields: id, book, user_id, currency, transaction, balance, notes, created_at, expire_at. The MEDICI_BALANCES table is used to track the financial balances associated with different users and their transactions in various currencies. It helps in managing and monitoring the financial records related to user accounts and their respective balances over time."}, {"table_name": "TRANSFORMED.OTC_VOLUMES", "description": "This table contains the following fields: id, from_symbol, to_symbol, country, rate, from_amount, to_amount, available_amount, user_id, timestamp, approved_by, submitted_by, updated_at, created_at. The OTC_VOLUMES table is designed to track over-the-counter (OTC) transaction volumes, capturing details about currency exchanges, the amounts involved, and the users responsible for the transactions."}, {"table_name": "TRANSFORMED.KYC_TIMELINES", "description": "This table contains the following fields: kyc_id, id, timestamp, state, processor, kyc_created_at, kyc_updated_at, kyc_status, user_country, title. The purpose of the KYC_TIMELINES table is to track the various stages and updates of the Know Your Customer (KYC) process for users, providing a timeline of events and statuses associated with each KYC submission."}, {"table_name": "TRANSFORMED.KYCS", "description": "This table contains the following fields: id, created_at, updated_at, user_id, status, details, date_of_birth, id_type, provider, attempts, id_number, success_acked, updated_by, bvn, ssn_last_four, is_kyc_doc_provided, user_country. The KYCS table is designed to store and manage Know Your Customer (KYC) information for users, including their identification details and the status of their verification process."}, {"table_name": "TRANSFORMED.REFERRALS", "description": "This table contains the following fields: id, updated_at, created_at, from_username, to_username, to_name, to_user_id, from_user_id, cumulative_transaction_amount, from_amount, to_amount, is_from_fulfilled, is_to_fulfilled, is_from_staff, year_fulfilled, status. The REFERRALS table is used to track referral transactions between users, capturing details about the referrer and the referred user, along with the financial aspects of the referral process and its fulfillment status."}, {"table_name": "TRANSFORMED.STORE_OWNERS_PAYOUT_TRACKER", "description": "This table contains the following fields: user_id, user_full_name, user_phone, user_signup_at, store_code, store_owner_id, store_owner_full_name, store_owner_email, transaction_id, transaction_status, transaction_created_at, paid_at, username. The purpose of this table is to track the payout transactions made to store owners, including details about the users and the status of each transaction."}, {"table_name": "TRANSFORMED.REFERRALS_TIERS", "description": "This table contains the following fields: id, created_at, updated_at, name, cumulative_threshold, transaction_window, bonus_amount_per_referree, bonus_amount_per_referrer, referral_code, currency_code, should_automate_payout, should_skip_referrer_payout, should_skip_referree_payout, is_deactivated, admin, type. The REFERRALS_TIERS table is designed to manage and define various tiers within a referral program, detailing the conditions and rewards associated with each tier for both referrers and referees."}, {"table_name": "TRANSFORMED.MEDICI_TRANSACTIONS", "description": "This table contains the following fields: id, credit, debit, meta, datetime, account_path, accounts, book, memo, journal, timestamp, voided, void_reason, original_journal, user_id, currency, date, week, month. The 'MEDICI_TRANSACTIONS' table is designed to store financial transaction records, capturing details about credits and debits associated with various accounts, along with metadata and timestamps for tracking purposes."}, {"table_name": "TRANSFORMED.METRIC_TRANSACTIONS", "description": "This table contains the following fields: id, created_at, date, week, month, duration, sender_id, sender_country, receiver_id, receiver_country, corridor_pair, type, afriex_type, processor, amount, currency, fulfillment_asset_value, fulfillment_asset, amount_usd, state, is_signup_bonus, from_to_usd, to_to_usd. The purpose of the METRIC_TRANSACTIONS table is to store detailed records of financial transactions, capturing essential metrics for analysis and reporting on transaction activities between senders and receivers across different corridors."}, {"table_name": "TRANSFORMED.USERS", "description": "This table contains the following fields: id, created_at, updated_at, username, country, email, phone, first_name, last_name, full_name, dob, referrer_id, kyc_status, kyc_updated_by, security_enabled, stripe_account_id, date, week, month, gender, device_os, phone_verified, tier_info, daily_limit, transaction_limit, account_username, account_referrer, kyc_verified_at, usernames, metadata. The USERS table is designed to store information about users of the application, including their personal details, account status, and verification information."}, {"table_name": "TRANSFORMED.TRANSACTIONS", "description": ""}]}], "tables": [{"table_id": "7ff8ea4f-2d28-48a2-8cfe-0df9d9328d4a", "table_name": "TRANSFORMED.AUTOMATE_RATE_TIER_DISCOUNT_RULES", "schema_id": "TRANSFORMED", "description": "This table contains the following fields: created_at, code, from_asset, to_asset, company_list, percent_higher_than_best, created_by, updated_by, updated_at. The purpose of this table is to define discount rules for rate tiers in an automated system, specifying the conditions under which discounts apply based on asset types and company criteria.", "field_count": 9, "fields": [{"name": "created_at", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the discount rule was created."}, {"name": "code", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for the discount rule."}, {"name": "from_asset", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The starting asset type for which the discount rule is applicable."}, {"name": "to_asset", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The ending asset type for which the discount rule is applicable."}, {"name": "company_list", "data_type": "ARRAY", "is_categorical": false, "is_datetime": false, "description": "A list of companies to which the discount rule applies."}, {"name": "percent_higher_than_best", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": "The percentage that the rate must be higher than the best available rate for the discount to apply."}, {"name": "created_by", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The identifier of the user who created the discount rule."}, {"name": "updated_by", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The identifier of the user who last updated the discount rule."}, {"name": "updated_at", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the discount rule was last updated."}], "sample_rows": []}, {"table_id": "34467686-f5ac-4ae4-95a7-9ceae9905d68", "table_name": "TRANSFORMED.AUTOMATE_RATE_TIER_DISCOUNT_LOG", "schema_id": "TRANSFORMED", "description": "This table contains the following fields: timestamp, code, from_asset, to_asset, discount, admin_discount, rule_by. The purpose of this table is to log discount tiers applied to asset exchanges, capturing relevant details about each discount event for auditing and analysis purposes.", "field_count": 7, "fields": [{"name": "timestamp", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The date and time when the discount was applied."}, {"name": "code", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "A unique identifier for the discount tier.", "categorical_values": ["CHURNED_USER", "NEW_USER"]}, {"name": "from_asset", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The asset type from which the discount is applied.", "categorical_values": ["USD"]}, {"name": "to_asset", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The asset type to which the discount is applied.", "categorical_values": ["NGN"]}, {"name": "discount", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": "The amount or percentage of discount applied."}, {"name": "admin_discount", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": "Any additional discount applied by an administrator."}, {"name": "rule_by", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The identifier of the rule or criteria that triggered the discount."}], "sample_rows": "{\n  \"timestamp\": \"2025-09-15T14:30:00\",\n  \"code\": \"ACTIVE_USER\",\n  \"from_asset\": \"EUR\",\n  \"to_asset\": \"JPY\",\n  \"discount\": 0.025478*********,\n  \"admin_discount\": 0.0225,\n  \"rule_by\": \"<EMAIL>\"\n}"}, {"table_id": "d44563c8-2d79-494f-bbd0-a94855042633", "table_name": "TRANSFORMED.MEDICI_BALANCES", "schema_id": "TRANSFORMED", "description": "This table contains the following fields: id, book, user_id, currency, transaction, balance, notes, created_at, expire_at. The MEDICI_BALANCES table is used to track the financial balances associated with different users and their transactions in various currencies. It helps in managing and monitoring the financial records related to user accounts and their respective balances over time.", "field_count": 9, "fields": [{"name": "id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each balance record."}, {"name": "book", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The name or identifier of the financial book or account associated with the balance.", "categorical_values": ["AfriexBook"]}, {"name": "user_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The unique identifier of the user to whom the balance belongs."}, {"name": "currency", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The currency in which the balance is denominated.", "categorical_values": ["CAD", "CNY", "EGP", "ETB", "EUR", "GBP", "GHS", "HTG", "INR", "KES", "MGA", "MWK", "MZN", "NGN", "PKR", "RWF", "TZS", "UGX", "USD", "XAF", "XOF", "ZAR", "ZMW"]}, {"name": "transaction", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The type or identifier of the transaction that affects the balance."}, {"name": "balance", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": "The current balance amount for the user in the specified currency."}, {"name": "notes", "data_type": "NUMBER", "is_categorical": true, "is_datetime": false, "description": "Any additional notes or comments related to the balance record.", "categorical_values": ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "50"]}, {"name": "created_at", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the balance record was created."}, {"name": "expire_at", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the balance record is set to expire or become invalid."}], "sample_rows": "{\n  \"id\": \"b1c2d3e4f5678g9h0i1j2k3l4\",\n  \"book\": \"DummyBook\",\n  \"user_id\": \"a1b2c3d4e5f67890abcdef12\",\n  \"currency\": \"EUR\",\n  \"transaction\": \"mno123pqr456stu789vwx0yz\",\n  \"balance\": 0.0023456700000009,\n  \"notes\": 45,\n  \"created_at\": \"2025-03-15T09:30:12.123000\",\n  \"expire_at\": \"2025-03-17T09:30:12.123000\"\n}"}, {"table_id": "96a50e53-6e98-45b3-a3ea-5c250f18251c", "table_name": "TRANSFORMED.OTC_VOLUMES", "schema_id": "TRANSFORMED", "description": "This table contains the following fields: id, from_symbol, to_symbol, country, rate, from_amount, to_amount, available_amount, user_id, timestamp, approved_by, submitted_by, updated_at, created_at. The OTC_VOLUMES table is designed to track over-the-counter (OTC) transaction volumes, capturing details about currency exchanges, the amounts involved, and the users responsible for the transactions.", "field_count": 14, "fields": [{"name": "id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each transaction record."}, {"name": "from_symbol", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The currency symbol of the currency being exchanged.", "categorical_values": ["USD"]}, {"name": "to_symbol", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The currency symbol of the currency being received.", "categorical_values": ["GHS", "NGN", "UGX"]}, {"name": "country", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The country associated with the currency exchange.", "categorical_values": ["GH", "NG", "UG", "US"]}, {"name": "rate", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The exchange rate applied to the transaction."}, {"name": "from_amount", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": "The amount of the currency being exchanged."}, {"name": "to_amount", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": "The amount of the currency being received after the exchange."}, {"name": "available_amount", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": "The amount of currency available for exchange."}, {"name": "user_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The identifier of the user who initiated the transaction."}, {"name": "timestamp", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The date and time when the transaction was recorded."}, {"name": "approved_by", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "The identifier of the user who approved the transaction."}, {"name": "submitted_by", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "The identifier of the user who submitted the transaction for processing."}, {"name": "updated_at", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The date and time when the transaction record was last updated."}, {"name": "created_at", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The date and time when the transaction record was created."}], "sample_rows": "{\n  \"id\": \"a1b2c3d4e5f67890abcdef12\",\n  \"from_symbol\": \"EUR\",\n  \"to_symbol\": \"JPY\",\n  \"country\": \"JP\",\n  \"rate\": \"162.50\",\n  \"from_amount\": 2500000,\n  \"to_amount\": 406250000,\n  \"available_amount\": 406250000,\n  \"user_id\": null,\n  \"timestamp\": \"2024-07-20T09:45:00\",\n  \"approved_by\": \"null\",\n  \"submitted_by\": \"{\\n  \\\"id\\\": \\\"abcdef*********0abcdef12\\\",\\n  \\\"name\\\": \\\"<PERSON><PERSON><PERSON>\\\",\\n  \\\"role\\\": \\\"finance.manager\\\"\\n}\",\n  \"updated_at\": \"2024-08-15T15:30:45.123000\",\n  \"created_at\": \"2024-07-10T12:00:00.000000\"\n}"}, {"table_id": "f6a26445-f390-4455-986d-e141be276668", "table_name": "TRANSFORMED.KYC_TIMELINES", "schema_id": "TRANSFORMED", "description": "This table contains the following fields: kyc_id, id, timestamp, state, processor, kyc_created_at, kyc_updated_at, kyc_status, user_country, title. The purpose of the KYC_TIMELINES table is to track the various stages and updates of the Know Your Customer (KYC) process for users, providing a timeline of events and statuses associated with each KYC submission.", "field_count": 10, "fields": [{"name": "kyc_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each KYC submission."}, {"name": "id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each record in the KYC_TIMELINES table."}, {"name": "timestamp", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The date and time when the KYC status was updated."}, {"name": "state", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The current state of the KYC process (e.g., pending, approved, rejected).", "categorical_values": ["failure", "notStarted", "pending", "rejected", "reuploadRequested", "submitted", "success"]}, {"name": "processor", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The name or identifier of the processor handling the KYC submission.", "categorical_values": ["admin", "smile", "stripe", "sumsub", "veriff"]}, {"name": "kyc_created_at", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The date and time when the KYC submission was initially created."}, {"name": "kyc_updated_at", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The date and time when the KYC submission was last updated."}, {"name": "kyc_status", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The overall status of the KYC process (e.g., in progress, completed).", "categorical_values": ["failure", "notStarted", "pending", "rejected", "reuploadRequested", "reuploaded", "started", "submitted", "success"]}, {"name": "user_country", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The country of the user submitting the KYC information.", "categorical_values": ["AE", "AF", "AG", "AI", "AL", "AM", "AO", "AR", "AT", "AU", "AZ", "BA", "BB", "BD", "BE", "BF", "BG", "BH", "BI", "BJ", "BM", "BO", "BR", "BS", "BW", "BY", "CA", "CD", "CF", "CG", "CH", "CI", "CL", "CM", "CN", "CO", "CR", "CU", "CV", "CW", "CY", "CZ", "DE", "DJ", "DK", "DM", "DO", "DZ", "EC", "EE"]}, {"name": "title", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "A brief title or description of the KYC submission.", "categorical_values": ["Date of Birth Mismatch", "Error on Veriff Session Creation", "Failed to Submit Documents to Veriff", "Failed to Upload Images to Veriff", "Name mismatch", "Receives <PERSON><PERSON><PERSON>", "Resubmission Requested", "Starts <PERSON><PERSON><PERSON> Verification", "Status updated", "Stripe Verification Completed", "<PERSON><PERSON><PERSON>", "Verification Abandoned", "Verification Approved", "Verification Declined", "Verification Expired", "Verification Started", "Verification Submitted", "failed", "in_review", "pending", "started", "submitted", "unverified", "verified"]}], "sample_rows": "{\n  \"kyc_id\": \"a1b2c3d4e5f67890abcdef1234\",\n  \"id\": \"12345678-90ab-cdef-1234-567890abcdef\",\n  \"timestamp\": \"2023-11-25T12:34:56.789000\",\n  \"state\": \"success\",\n  \"processor\": \"exampleProcessor\",\n  \"kyc_created_at\": \"2023-11-20T12:00:00.000000\",\n  \"kyc_updated_at\": \"2023-11-25T12:34:56.789000\",\n  \"kyc_status\": \"success\",\n  \"user_country\": \"US\",\n  \"title\": \"Verification Completed\"\n}"}, {"table_id": "3a3f895c-a5e4-4dce-adc7-57c599c3ac1a", "table_name": "TRANSFORMED.KYCS", "schema_id": "TRANSFORMED", "description": "This table contains the following fields: id, created_at, updated_at, user_id, status, details, date_of_birth, id_type, provider, attempts, id_number, success_acked, updated_by, bvn, ssn_last_four, is_kyc_doc_provided, user_country. The KYCS table is designed to store and manage Know Your Customer (KYC) information for users, including their identification details and the status of their verification process.", "field_count": 17, "fields": [{"name": "id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each KYC record."}, {"name": "created_at", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The timestamp when the KYC record was created."}, {"name": "updated_at", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The timestamp when the KYC record was last updated."}, {"name": "user_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The unique identifier of the user associated with this KYC record."}, {"name": "status", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The current status of the KYC verification process (e.g., pending, approved, rejected).", "categorical_values": ["failure", "notStarted", "pending", "rejected", "reuploadRequested", "reuploaded", "started", "submitted", "success"]}, {"name": "details", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "Additional details or notes regarding the KYC process."}, {"name": "date_of_birth", "data_type": "DATE", "is_categorical": false, "is_datetime": true, "description": "The date of birth of the user."}, {"name": "id_type", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The type of identification document provided (e.g., passport, driver's license).", "categorical_values": ["ALIEN_CARD", "BVN", "DRIVERS_LICENSE", "NATIONAL_ID", "NATIONAL_ID_NO_PHOTO", "NEW_VOTER_ID", "NIN", "PASSPORT", "Residence_permit", "SSNIT", "VOTER_ID", "bvn", "driverLicense", "drivers_license", "national_id", "national_id_no_photo", "new_voter_id", "nin", "passport", "residence_permit", "ssnit", "voter_id"]}, {"name": "provider", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The service provider or agency responsible for the KYC verification.", "categorical_values": ["admin", "smile", "stripe", "sumsub", "veriff"]}, {"name": "attempts", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": "The number of attempts made to verify the user's identity."}, {"name": "id_number", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The identification number from the provided ID document."}, {"name": "success_acked", "data_type": "BOOLEAN", "is_categorical": false, "is_datetime": false, "description": "A flag indicating whether the KYC verification was successfully acknowledged."}, {"name": "updated_by", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The identifier of the user or system that last updated the KYC record."}, {"name": "bvn", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The Bank Verification Number associated with the user."}, {"name": "ssn_last_four", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The last four digits of the user's Social Security Number."}, {"name": "is_kyc_doc_provided", "data_type": "BOOLEAN", "is_categorical": false, "is_datetime": false, "description": "A flag indicating whether the user has provided the necessary KYC documents."}, {"name": "user_country", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The country of residence of the user.", "categorical_values": ["AE", "AF", "AG", "AI", "AL", "AM", "AO", "AR", "AT", "AU", "AZ", "BA", "BB", "BD", "BE", "BF", "BG", "BH", "BI", "BJ", "BM", "BO", "BR", "BS", "BW", "BY", "CA", "CD", "CF", "CG", "CH", "CI", "CL", "CM", "CN", "CO", "CR", "CU", "CV", "CW", "CY", "CZ", "DE", "DJ", "DK", "DM", "DO", "DZ", "EC", "EE"]}], "sample_rows": "{\n  \"id\": \"b1a2c3d4e5f67890abcdef12\",\n  \"created_at\": \"2024-09-15T14:25:30.123456\",\n  \"updated_at\": \"2024-09-15T16:45:50.654321\",\n  \"user_id\": \"b1a2c3d4e5f67890abcdef12\",\n  \"status\": \"failure\",\n  \"details\": null,\n  \"date_of_birth\": \"1990-12-01\",\n  \"id_type\": \"passport\",\n  \"provider\": \"user\",\n  \"attempts\": null,\n  \"id_number\": \"***********\",\n  \"success_acked\": null,\n  \"updated_by\": \"a1b2c3d4e5f67890abcdef12\",\n  \"bvn\": null,\n  \"ssn_last_four\": null,\n  \"is_kyc_doc_provided\": null,\n  \"user_country\": \"US\"\n}"}, {"table_id": "8f912877-ea30-4f2f-8180-36b3d7918d1c", "table_name": "TRANSFORMED.REFERRALS", "schema_id": "TRANSFORMED", "description": "This table contains the following fields: id, updated_at, created_at, from_username, to_username, to_name, to_user_id, from_user_id, cumulative_transaction_amount, from_amount, to_amount, is_from_fulfilled, is_to_fulfilled, is_from_staff, year_fulfilled, status. The REFERRALS table is used to track referral transactions between users, capturing details about the referrer and the referred user, along with the financial aspects of the referral process and its fulfillment status.", "field_count": 16, "fields": [{"name": "id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each referral record."}, {"name": "updated_at", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the referral record was last updated."}, {"name": "created_at", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the referral record was created."}, {"name": "from_username", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The username of the user who made the referral."}, {"name": "to_username", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The username of the user who is being referred."}, {"name": "to_name", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The full name of the user who is being referred."}, {"name": "to_user_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The unique identifier of the user who is being referred."}, {"name": "from_user_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The unique identifier of the user who made the referral."}, {"name": "cumulative_transaction_amount", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": "The total amount of transactions associated with the referral."}, {"name": "from_amount", "data_type": "NUMBER", "is_categorical": true, "is_datetime": false, "description": "The amount of money associated with the referrer.", "categorical_values": ["0.0", "1.0", "2.0", "5.0", "10.0", "15.0", "20.0", "35.0"]}, {"name": "to_amount", "data_type": "NUMBER", "is_categorical": true, "is_datetime": false, "description": "The amount of money associated with the referred user.", "categorical_values": ["0.0", "5.0", "10.0", "15.0", "20.0", "35.0"]}, {"name": "is_from_fulfilled", "data_type": "BOOLEAN", "is_categorical": true, "is_datetime": false, "description": "A boolean indicating whether the referral from the referrer has been fulfilled.", "categorical_values": ["True"]}, {"name": "is_to_fulfilled", "data_type": "BOOLEAN", "is_categorical": true, "is_datetime": false, "description": "A boolean indicating whether the referral to the referred user has been fulfilled.", "categorical_values": ["True"]}, {"name": "is_from_staff", "data_type": "BOOLEAN", "is_categorical": true, "is_datetime": false, "description": "A boolean indicating whether the referrer is a staff member.", "categorical_values": ["False", "True"]}, {"name": "year_fulfilled", "data_type": "NUMBER", "is_categorical": true, "is_datetime": false, "description": "The year in which the referral was fulfilled.", "categorical_values": ["2023", "2024", "2025"]}, {"name": "status", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The current status of the referral (e.g., pending, completed, canceled).", "categorical_values": ["joined", "qualified", "resolved", "transacted"]}], "sample_rows": "{\n  \"id\": \"a1b2c3d4e5f67890abcdef12\",\n  \"updated_at\": \"2024-02-15T10:30:45.123000\",\n  \"created_at\": \"2023-10-10T14:22:18.456000\",\n  \"from_username\": \"randomUser123\",\n  \"to_username\": \"exampleUser456\",\n  \"to_name\": \"Example User\",\n  \"to_user_id\": \"a1b2c3d4e5f67890abcdef34\",\n  \"from_user_id\": \"f1e2d3c4b5a67890abcdef56\",\n  \"cumulative_transaction_amount\": 1234.56,\n  \"from_amount\": 10.0,\n  \"to_amount\": 10.0,\n  \"is_from_fulfilled\": false,\n  \"is_to_fulfilled\": true,\n  \"is_from_staff\": true,\n  \"year_fulfilled\": 2025,\n  \"status\": \"pending\"\n}"}, {"table_id": "524579ae-46fe-4ceb-ae33-590b155071dd", "table_name": "TRANSFORMED.STORE_OWNERS_PAYOUT_TRACKER", "schema_id": "TRANSFORMED", "description": "This table contains the following fields: user_id, user_full_name, user_phone, user_signup_at, store_code, store_owner_id, store_owner_full_name, store_owner_email, transaction_id, transaction_status, transaction_created_at, paid_at, username. The purpose of this table is to track the payout transactions made to store owners, including details about the users and the status of each transaction.", "field_count": 13, "fields": [{"name": "user_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for the user associated with the payout."}, {"name": "user_full_name", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The full name of the user receiving the payout."}, {"name": "user_phone", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The phone number of the user for contact purposes."}, {"name": "user_signup_at", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the user signed up."}, {"name": "store_code", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "A unique code representing the store associated with the payout.", "categorical_values": ["afrocan", "aio", "ameen", "berry", "bonje", "choice", "ebeano", "eko", "forks", "hairport", "linkup", "mo", "noi", "plush", "ruumors", "temmy", "up2u"]}, {"name": "store_owner_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for the store owner."}, {"name": "store_owner_full_name", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The full name of the store owner."}, {"name": "store_owner_email", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The email address of the store owner."}, {"name": "transaction_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for the transaction."}, {"name": "transaction_status", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The current status of the transaction (e.g., pending, completed).", "categorical_values": ["SUCCESS"]}, {"name": "transaction_created_at", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the transaction was created."}, {"name": "paid_at", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the payout was made."}, {"name": "username", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The username of the user associated with the payout."}], "sample_rows": "{\n  \"user_id\": \"a1b2c3d4e5f67890abcdef12\",\n  \"user_full_name\": \"<PERSON>\",\n  \"user_phone\": \"+14155551234\",\n  \"user_signup_at\": \"2025-06-15T09:45:12.123000\",\n  \"store_code\": \"apple\",\n  \"store_owner_id\": \"f1e2d3c4b5a67890abcdef34\",\n  \"store_owner_full_name\": \"<PERSON>\",\n  \"store_owner_email\": \"jane<PERSON>@example.com\",\n  \"transaction_id\": \"b1c2d3e4f567890abcdef56\",\n  \"transaction_status\": \"PENDING\",\n  \"transaction_created_at\": \"2025-06-15T10:00:00.456000\",\n  \"paid_at\": \"2025-06-15T10:15:00\",\n  \"username\": \"user12345\"\n}"}, {"table_id": "590d2881-0c1f-40d0-924e-f044d061435b", "table_name": "TRANSFORMED.REFERRALS_TIERS", "schema_id": "TRANSFORMED", "description": "This table contains the following fields: id, created_at, updated_at, name, cumulative_threshold, transaction_window, bonus_amount_per_referree, bonus_amount_per_referrer, referral_code, currency_code, should_automate_payout, should_skip_referrer_payout, should_skip_referree_payout, is_deactivated, admin, type. The REFERRALS_TIERS table is designed to manage and define various tiers within a referral program, detailing the conditions and rewards associated with each tier for both referrers and referees.", "field_count": 16, "fields": [{"name": "id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each referral tier."}, {"name": "created_at", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the referral tier was created."}, {"name": "updated_at", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating the last time the referral tier was updated."}, {"name": "name", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The name of the referral tier."}, {"name": "cumulative_threshold", "data_type": "NUMBER", "is_categorical": true, "is_datetime": false, "description": "The cumulative amount that must be reached for the tier to be activated.", "categorical_values": ["5.0", "10.0", "20.0", "30.0", "50.0", "100.0", "150.0", "10000.0"]}, {"name": "transaction_window", "data_type": "NUMBER", "is_categorical": true, "is_datetime": false, "description": "The time frame in which transactions must occur to qualify for the tier.", "categorical_values": ["0.0", "30.0", "100.0", "200.0", "300.0", "357.0", "358.0", "360.0", "361.0", "362.0", "363.0", "364.0", "365.0", "366.0", "368.0", "369.0", "3654.0"]}, {"name": "bonus_amount_per_referree", "data_type": "NUMBER", "is_categorical": true, "is_datetime": false, "description": "The bonus amount awarded for each successful referral made by a referee.", "categorical_values": ["1.0", "5.0", "10.0", "15.0", "20.0", "35.0"]}, {"name": "bonus_amount_per_referrer", "data_type": "NUMBER", "is_categorical": true, "is_datetime": false, "description": "The bonus amount awarded for each successful referral made by a referrer.", "categorical_values": ["1.0", "2.0", "5.0", "10.0", "15.0", "20.0", "35.0"]}, {"name": "referral_code", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "A unique code associated with the referral tier."}, {"name": "currency_code", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The currency in which the bonuses are awarded.", "categorical_values": ["USD"]}, {"name": "should_automate_payout", "data_type": "BOOLEAN", "is_categorical": true, "is_datetime": false, "description": "Indicates whether payouts for this tier should be automated.", "categorical_values": ["False", "True"]}, {"name": "should_skip_referrer_payout", "data_type": "BOOLEAN", "is_categorical": true, "is_datetime": false, "description": "Indicates whether the payout to the referrer should be skipped.", "categorical_values": ["False", "True"]}, {"name": "should_skip_referree_payout", "data_type": "BOOLEAN", "is_categorical": true, "is_datetime": false, "description": "Indicates whether the payout to the referee should be skipped.", "categorical_values": ["False", "True"]}, {"name": "is_deactivated", "data_type": "BOOLEAN", "is_categorical": true, "is_datetime": false, "description": "Indicates whether the referral tier is currently deactivated.", "categorical_values": ["False", "True"]}, {"name": "admin", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "The identifier of the admin who created or manages the referral tier."}, {"name": "type", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The type of referral tier, which may define its characteristics or rules.", "categorical_values": ["merchant-aggregator", "referral"]}], "sample_rows": "{\n  \"id\": \"f3b2c4d5e6f7g8h9i0j1k2l3\",\n  \"created_at\": \"2024-09-15T10:20:30.123000\",\n  \"updated_at\": \"2024-10-20T14:55:45.678000\",\n  \"name\": \"<PERSON>\",\n  \"cumulative_threshold\": 200.0,\n  \"transaction_window\": 400.0,\n  \"bonus_amount_per_referree\": 25.0,\n  \"bonus_amount_per_referrer\": 15.0,\n  \"referral_code\": \"alexs\",\n  \"currency_code\": \"EUR\",\n  \"should_automate_payout\": false,\n  \"should_skip_referrer_payout\": false,\n  \"should_skip_referree_payout\": true,\n  \"is_deactivated\": null,\n  \"admin\": \"\\\"{\\\\\\\"id\\\\\\\":\\\\\\\"7e8f9a0b1c2d3e4f5g6h7i8j\\\\\\\",\\\\\\\"name\\\\\\\":\\\\\\\"JaneDoe\\\\\\\",\\\\\\\"role\\\\\\\":\\\\\\\"marketing.lead\\\\\\\"}\\\"\",\n  \"type\": \"referral\"\n}"}, {"table_id": "da3dbc4a-0ed2-4e46-927c-1357587f83ef", "table_name": "TRANSFORMED.MEDICI_TRANSACTIONS", "schema_id": "TRANSFORMED", "description": "This table contains the following fields: id, credit, debit, meta, datetime, account_path, accounts, book, memo, journal, timestamp, voided, void_reason, original_journal, user_id, currency, date, week, month. The 'MEDICI_TRANSACTIONS' table is designed to store financial transaction records, capturing details about credits and debits associated with various accounts, along with metadata and timestamps for tracking purposes.", "field_count": 19, "fields": [{"name": "id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each transaction."}, {"name": "credit", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": "The amount credited in the transaction."}, {"name": "debit", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": "The amount debited in the transaction."}, {"name": "meta", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "Additional metadata related to the transaction."}, {"name": "datetime", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The exact date and time when the transaction occurred."}, {"name": "account_path", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "The hierarchical path of the account associated with the transaction."}, {"name": "accounts", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "A list of accounts involved in the transaction."}, {"name": "book", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The financial book or ledger to which the transaction belongs.", "categorical_values": ["AfriexBook"]}, {"name": "memo", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "A brief note or description regarding the transaction."}, {"name": "journal", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The journal entry associated with the transaction."}, {"name": "timestamp", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "A timestamp indicating when the transaction record was created or modified."}, {"name": "voided", "data_type": "BOOLEAN", "is_categorical": true, "is_datetime": false, "description": "A boolean indicating whether the transaction has been voided.", "categorical_values": ["True"]}, {"name": "void_reason", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The reason for voiding the transaction, if applicable."}, {"name": "original_journal", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The original journal entry from which this transaction was created."}, {"name": "user_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The identifier of the user who created or modified the transaction."}, {"name": "currency", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The currency in which the transaction is denominated.", "categorical_values": ["CAD", "CNY", "EGP", "ETB", "EUR", "GBP", "GHS", "HTG", "INR", "KES", "MGA", "MWK", "MZN", "NGN", "PKR", "RWF", "TZS", "UGX", "USD", "XAF", "XOF", "ZAR", "ZMW"]}, {"name": "date", "data_type": "DATE", "is_categorical": false, "is_datetime": true, "description": "The date of the transaction, typically without time."}, {"name": "week", "data_type": "DATE", "is_categorical": false, "is_datetime": true, "description": "The week number of the year when the transaction occurred."}, {"name": "month", "data_type": "DATE", "is_categorical": false, "is_datetime": true, "description": "The month number of the year when the transaction occurred."}], "sample_rows": "{\n  \"id\": \"a1b2c3d4e5f6789012345678\",\n  \"credit\": 75.0,\n  \"debit\": 0.0,\n  \"meta\": \"{\\n  \\\"channel\\\": \\\"CREDIT_CARD\\\",\\n  \\\"destinationAmount\\\": \\\"12345.678901\\\",\\n  \\\"destinationAsset\\\": \\\"EUR\\\",\\n  \\\"paymentMethodId\\\": \\\"abcdef*********0abcdef12\\\",\\n  \\\"processor\\\": \\\"PAYPAL\\\",\\n  \\\"receiverId\\\": \\\"abcdefabcdefabcdefabcdef12\\\",\\n  \\\"senderId\\\": \\\"*********0abcdefabcdef12\\\",\\n  \\\"sourceAmount\\\": 75,\\n  \\\"sourceAsset\\\": \\\"GBP\\\",\\n  \\\"transactionId\\\": \\\"Random-*********0abcdefabcdef12-*************\\\"\\n}\",\n  \"datetime\": \"2025-01-15T09:30:45.123000\",\n  \"account_path\": \"[\\n  \\\"abcdefabcdefabcdefabcdef12\\\",\\n  \\\"GBP\\\"\\n]\",\n  \"accounts\": \"abcdefabcdefabcdefabcdef12:GBP\",\n  \"book\": \"RandomBook\",\n  \"memo\": \"deposit\",\n  \"journal\": \"a1b2c3d4e5f6789012345679\",\n  \"timestamp\": \"2025-01-15T09:30:45.123000\",\n  \"voided\": false,\n  \"void_reason\": \"null\",\n  \"original_journal\": null,\n  \"user_id\": \"abcdefabcdefabcdefabcdef12\",\n  \"currency\": \"GBP\",\n  \"date\": \"2025-01-15\",\n  \"week\": \"2025-01-12\",\n  \"month\": \"2025-01-01\"\n}"}, {"table_id": "1ef1949f-6d6b-40f7-afac-03d3548b4e50", "table_name": "TRANSFORMED.METRIC_TRANSACTIONS", "schema_id": "TRANSFORMED", "description": "This table contains the following fields: id, created_at, date, week, month, duration, sender_id, sender_country, receiver_id, receiver_country, corridor_pair, type, afriex_type, processor, amount, currency, fulfillment_asset_value, fulfillment_asset, amount_usd, state, is_signup_bonus, from_to_usd, to_to_usd. The purpose of the METRIC_TRANSACTIONS table is to store detailed records of financial transactions, capturing essential metrics for analysis and reporting on transaction activities between senders and receivers across different corridors.", "field_count": 23, "fields": [{"name": "id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each transaction record."}, {"name": "created_at", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the transaction record was created."}, {"name": "date", "data_type": "DATE", "is_categorical": false, "is_datetime": true, "description": "The date on which the transaction took place."}, {"name": "week", "data_type": "DATE", "is_categorical": false, "is_datetime": true, "description": "The week number of the year when the transaction occurred."}, {"name": "month", "data_type": "DATE", "is_categorical": false, "is_datetime": true, "description": "The month number of the year when the transaction occurred."}, {"name": "duration", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": "The duration of the transaction process, typically measured in seconds."}, {"name": "sender_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The unique identifier of the sender involved in the transaction."}, {"name": "sender_country", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The country from which the sender is originating.", "categorical_values": ["AT", "BE", "CA", "CI", "CM", "CN", "CY", "DE", "EE", "EG", "ES", "ET", "FI", "FR", "GB", "GH", "GR", "HR", "IE", "IN", "IT", "KE", "LT", "LU", "LV", "MG", "MT", "NG", "NL", "PK", "PL", "PT", "RW", "SI", "SK", "SN", "TZ", "UG", "US", "ZA"]}, {"name": "receiver_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The unique identifier of the receiver involved in the transaction."}, {"name": "receiver_country", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The country where the receiver is located.", "categorical_values": ["AT", "BJ", "CA", "CI", "CM", "CN", "DE", "EG", "ES", "ET", "FR", "GB", "GH", "HT", "IE", "IN", "KE", "MG", "MW", "MZ", "NG", "NL", "PH", "PK", "RW", "SN", "TZ", "UG", "US", "ZA", "ZM"]}, {"name": "corridor_pair", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The pair of countries representing the transaction corridor.", "categorical_values": ["CAD-->CAD", "CAD-->ETB", "CAD-->EUR", "CAD-->GBP", "CAD-->GHS", "CAD-->INR", "CAD-->KES", "CAD-->MWK", "CAD-->NGN", "CAD-->PKR", "CAD-->RWF", "CAD-->TZS", "CAD-->UGX", "CAD-->USD", "CAD-->XAF", "CAD-->XOF", "CAD-->ZAR", "CAD-->ZMW", "CNY-->ETB", "CNY-->NGN", "EGP-->EGP", "EGP-->ETB", "EGP-->KES", "EGP-->NGN", "ETB-->CAD", "ETB-->EGP", "ETB-->ETB", "ETB-->GBP", "ETB-->GHS", "ETB-->INR", "ETB-->KES", "ETB-->MGA", "ETB-->NGN", "ETB-->RWF", "ETB-->UGX", "ETB-->USD", "ETB-->XAF", "EUR-->CAD", "EUR-->ETB", "EUR-->EUR", "EUR-->GBP", "EUR-->GHS", "EUR-->KES", "EUR-->MZN", "EUR-->NGN", "EUR-->PKR", "EUR-->RWF", "EUR-->UGX", "EUR-->USD", "EUR-->XAF"]}, {"name": "type", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The type of transaction, indicating the nature of the transfer.", "categorical_values": ["ADMIN_DEPOSIT", "ADMIN_REFERRAL", "ADMIN_WELCOME_BONUS", "ADMIN_WITHDRAW", "DEPOSIT", "SWAP", "USER TO USER", "WITHDRAWAL"]}, {"name": "afriex_type", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The specific type of transaction as defined by Afriex.", "categorical_values": ["ADMIN_DEPOSIT", "ADMIN_REFERRAL", "ADMIN_WELCOME_BONUS", "ADMIN_WITHDRAW", "DEPOSIT", "REFERRAL", "REVERSAL", "SWAP", "TRANSFER", "VIRTUAL_CARD_LOAD", "VIRTUAL_CARD_REFUND", "VIRTUAL_CARD_SPEND", "VIRTUAL_CARD_UNLOAD", "WITHDRAW"]}, {"name": "processor", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The payment processor used to facilitate the transaction.", "categorical_values": ["ALPAY", "BEYONIC", "BRIDGECARD", "CELLULANT", "CHAPA", "DLOCAL", "FAIRMONEY", "FINCRA", "FLICK", "HUB2", "JUICYWAY", "KORAPAY", "MONIEPOINT", "MONO", "ONAFRIQ", "PAYAZA", "PAYSTACK", "PROVIDUS", "STRIPE", "TERRAPAY", "TRICE", "UNKNOWN", "VFD", "WORLDPAY", "ZEEPAY", "ZENITH", "ZILMONEY"]}, {"name": "amount", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": "The total amount of money being transferred in the transaction."}, {"name": "currency", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The currency in which the transaction amount is denominated.", "categorical_values": ["CAD", "CNY", "EGP", "ETB", "EUR", "GBP", "GHS", "INR", "KES", "MGA", "NGN", "PKR", "RWF", "TZS", "UGX", "USD", "XAF", "XOF", "ZAR"]}, {"name": "fulfillment_asset_value", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": "The value of the asset used to fulfill the transaction."}, {"name": "fulfillment_asset", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The type of asset used to complete the transaction.", "categorical_values": ["CAD", "CNY", "EGP", "ETB", "EUR", "GBP", "GHS", "HTG", "INR", "KES", "MGA", "MWK", "MZN", "NGN", "PHP", "PKR", "RWF", "TZS", "UGX", "USD", "XAF", "XOF", "ZAR", "ZMW"]}, {"name": "amount_usd", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": "The equivalent amount of the transaction in US dollars."}, {"name": "state", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The current state of the transaction, indicating its status.", "categorical_values": ["CANCELLED", "DISPUTE_EVIDENCE_SUBMITTED", "DISPUTE_LOST", "DISPUTE_RESOLVED", "DISPUTE_WON", "FAILED", "IN_REVIEW", "PENDING", "PROCESSING", "REFUNDED", "RETRY", "SUCCESS", "UNCLEAR", "UNKNOWN"]}, {"name": "is_signup_bonus", "data_type": "BOOLEAN", "is_categorical": false, "is_datetime": false, "description": "A flag indicating whether the transaction includes a signup bonus."}, {"name": "from_to_usd", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": "The conversion rate from the original currency to USD for the transaction."}, {"name": "to_to_usd", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": "The conversion rate to USD for the final amount received."}], "sample_rows": "{\n  \"id\": \"a1b2c3d4e5f60708090a1b2c3\",\n  \"created_at\": \"2023-04-20T12:34:56.789000\",\n  \"date\": \"2023-04-20\",\n  \"week\": \"2023-04-17\",\n  \"month\": \"2023-04-01\",\n  \"duration\": ***********,\n  \"sender_id\": \"b2c3d4e5f60708090a1b2c3d\",\n  \"sender_country\": \"CA\",\n  \"receiver_id\": \"b2c3d4e5f60708090a1b2c3d\",\n  \"receiver_country\": \"GB\",\n  \"corridor_pair\": \"CAD-->GBP\",\n  \"type\": \"DEPOSIT\",\n  \"afriex_type\": \"DEPOSIT\",\n  \"processor\": \"BANKXYZ\",\n  \"amount\": 350.0,\n  \"currency\": \"CAD\",\n  \"fulfillment_asset_value\": 250000.00,\n  \"fulfillment_asset\": \"GBP\",\n  \"amount_usd\": 350.0,\n  \"state\": \"PENDING\",\n  \"is_signup_bonus\": null,\n  \"from_to_usd\": null,\n  \"to_to_usd\": null\n}"}, {"table_id": "205b7449-718f-47fb-9e14-e39bc425808a", "table_name": "TRANSFORMED.USERS", "schema_id": "TRANSFORMED", "description": "This table contains the following fields: id, created_at, updated_at, username, country, email, phone, first_name, last_name, full_name, dob, referrer_id, kyc_status, kyc_updated_by, security_enabled, stripe_account_id, date, week, month, gender, device_os, phone_verified, tier_info, daily_limit, transaction_limit, account_username, account_referrer, kyc_verified_at, usernames, metadata. The USERS table is designed to store information about users of the application, including their personal details, account status, and verification information.", "field_count": 30, "fields": [{"name": "id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each user in the table."}, {"name": "created_at", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the user account was created."}, {"name": "updated_at", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating the last time the user account was updated."}, {"name": "username", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The unique username chosen by the user for login purposes."}, {"name": "country", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The country where the user resides.", "categorical_values": ["AAA", "AE", "AF", "AG", "AI", "AL", "AM", "AO", "AR", "AT", "AU", "AZ", "BA", "BB", "BD", "BE", "BF", "BG", "BH", "BI", "BJ", "BM", "BO", "BR", "BS", "BW", "BY", "CA", "CD", "CF", "CG", "CH", "CI", "CL", "CM", "CN", "CO", "CR", "CU", "CV", "CW", "CY", "CZ", "DE", "DJ", "DK", "DM", "DO", "DZ", "EC"]}, {"name": "email", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The email address associated with the user's account."}, {"name": "phone", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The phone number provided by the user."}, {"name": "first_name", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The first name of the user."}, {"name": "last_name", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The last name of the user."}, {"name": "full_name", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The full name of the user, typically a combination of first and last name."}, {"name": "dob", "data_type": "DATE", "is_categorical": false, "is_datetime": true, "description": "The date of birth of the user."}, {"name": "referrer_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The identifier of the user who referred this user to the platform."}, {"name": "kyc_status", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The Know Your Customer (KYC) verification status of the user.", "categorical_values": ["failure", "notStarted", "pending", "rejected", "reuploadRequested", "reuploaded", "started", "submitted", "success"]}, {"name": "kyc_updated_by", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The identifier of the user or admin who last updated the KYC status."}, {"name": "security_enabled", "data_type": "BOOLEAN", "is_categorical": true, "is_datetime": false, "description": "A flag indicating whether security features are enabled for the user.", "categorical_values": ["False", "True"]}, {"name": "stripe_account_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The identifier for the user's Stripe account, if applicable."}, {"name": "date", "data_type": "DATE", "is_categorical": false, "is_datetime": true, "description": "The date of the last activity or transaction by the user."}, {"name": "week", "data_type": "DATE", "is_categorical": false, "is_datetime": true, "description": "The week number of the year for the user's last activity."}, {"name": "month", "data_type": "DATE", "is_categorical": false, "is_datetime": true, "description": "The month number of the year for the user's last activity."}, {"name": "gender", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The gender of the user."}, {"name": "device_os", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The operating system of the device used by the user.", "categorical_values": ["ANDROID", "IOS"]}, {"name": "phone_verified", "data_type": "BOOLEAN", "is_categorical": true, "is_datetime": false, "description": "A flag indicating whether the user's phone number has been verified.", "categorical_values": ["False", "True"]}, {"name": "tier_info", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "Information about the user's tier or subscription level."}, {"name": "daily_limit", "data_type": "NUMBER", "is_categorical": true, "is_datetime": false, "description": "The daily transaction limit set for the user.", "categorical_values": ["1000.0", "3000.0", "5000.0", "7000.0", "10000.0", "30000.0", "500000.0"]}, {"name": "transaction_limit", "data_type": "NUMBER", "is_categorical": true, "is_datetime": false, "description": "The overall transaction limit for the user.", "categorical_values": ["400.0", "1000.0", "2000.0", "3000.0", "30000.0"]}, {"name": "account_username", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The username associated with the user's account for external services."}, {"name": "account_referrer", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The referrer associated with the user's account for external services."}, {"name": "kyc_verified_at", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the user was last verified for KYC."}, {"name": "usernames", "data_type": "ARRAY", "is_categorical": false, "is_datetime": false, "description": "A list of alternative usernames associated with the user."}, {"name": "metadata", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "Additional metadata related to the user, stored in a key-value format."}], "sample_rows": "{\n  \"id\": \"f3a2b1c4d5e6f7g8h9i0j1k2l\",\n  \"created_at\": \"2023-09-15T14:12:34.567000\",\n  \"updated_at\": \"2024-07-20T09:45:12.345000\",\n  \"username\": \"randomuser123\",\n  \"country\": \"CA\",\n  \"email\": \"<EMAIL>\",\n  \"phone\": \"+***********\",\n  \"first_name\": \"<PERSON>\",\n  \"last_name\": \"<PERSON><PERSON>\",\n  \"full_name\": \"<PERSON>\",\n  \"dob\": \"1985-05-15\",\n  \"referrer_id\": \"a1b2c3d4e5f67890abcdef12\",\n  \"kyc_status\": \"pending\",\n  \"kyc_updated_by\": \"abcdef*********0abcdef12\",\n  \"security_enabled\": true,\n  \"stripe_account_id\": \"acct_1XyZz1ABc5d6EfGh\",\n  \"date\": \"2023-09-15\",\n  \"week\": \"2023-09-11\",\n  \"month\": \"2023-09-01\",\n  \"gender\": null,\n  \"device_os\": \"ANDROID\",\n  \"phone_verified\": false,\n  \"tier_info\": \"{}\",\n  \"daily_limit\": 6000.0,\n  \"transaction_limit\": 2500.0,\n  \"account_username\": \"randomuser123\",\n  \"account_referrer\": \"example_referrer\",\n  \"kyc_verified_at\": \"2023-09-30T15:20:45.678000\",\n  \"usernames\": \"[]\",\n  \"metadata\": \"null\"\n}"}, {"table_id": "1b7121bd-4746-4348-8feb-d7790eed897e", "table_name": "TRANSFORMED.TRANSACTIONS", "schema_id": "TRANSFORMED", "description": "", "field_count": 47, "fields": [{"name": "id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "created_at", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": ""}, {"name": "updated_at", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": ""}, {"name": "amount", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "currency", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "", "categorical_values": ["CAD", "CNY", "EGP", "ETB", "EUR", "GBP", "GHS", "INR", "KES", "MGA", "NGN", "PKR", "RWF", "TZS", "UGX", "USD", "XAF", "XOF", "ZAR"]}, {"name": "amount_usd", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "fulfillment_asset_value", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "fulfillment_asset", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "", "categorical_values": ["CAD", "CNY", "EGP", "ETB", "EUR", "GBP", "GHS", "HTG", "INR", "KES", "MGA", "MWK", "MZN", "NGN", "PHP", "PKR", "RWF", "TZS", "UGX", "USD", "XAF", "XOF", "ZAR", "ZMW"]}, {"name": "fulfillment_asset_value_usd", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "recipient_amt", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "recipient_currency", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "recipient_amount_usd", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "processor", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "", "categorical_values": ["ALPAY", "BEYONIC", "BRIDGECARD", "CELLULANT", "CHAPA", "DLOCAL", "FAIRMONEY", "FINCRA", "FLICK", "HUB2", "JUICYWAY", "KORAPAY", "MONIEPOINT", "MONO", "ONAFRIQ", "PAYAZA", "PAYSTACK", "PROVIDUS", "STRIPE", "TERRAPAY", "TRICE", "UNKNOWN", "VFD", "WORLDPAY", "ZEEPAY", "ZENITH", "ZILMONEY"]}, {"name": "transaction_type", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "", "categorical_values": ["ADMIN_DEPOSIT", "ADMIN_REFERRAL", "ADMIN_WELCOME_BONUS", "ADMIN_WITHDRAW", "DEPOSIT", "REFERRAL", "REVERSAL", "SWAP", "TRANSFER", "VIRTUAL_CARD_LOAD", "VIRTUAL_CARD_REFUND", "VIRTUAL_CARD_SPEND", "VIRTUAL_CARD_UNLOAD", "WITHDRAW"]}, {"name": "state", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "", "categorical_values": ["CANCELLED", "DISPUTE_EVIDENCE_SUBMITTED", "DISPUTE_LOST", "DISPUTE_RESOLVED", "DISPUTE_WON", "FAILED", "IN_REVIEW", "PENDING", "PROCESSING", "REFUNDED", "RETRY", "SUCCESS", "UNCLEAR", "UNKNOWN"]}, {"name": "user_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "sender_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "source_country", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "", "categorical_values": ["AT", "BE", "CA", "CI", "CM", "CN", "CY", "DE", "EE", "EG", "ES", "ET", "FI", "FR", "GB", "GH", "GR", "HR", "IE", "IN", "IT", "KE", "LT", "LU", "LV", "MG", "MT", "NG", "NL", "PK", "PL", "PT", "RW", "SI", "SK", "SN", "TZ", "UG", "US", "ZA"]}, {"name": "receiver_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "recipient_country", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "", "categorical_values": ["AT", "BJ", "CA", "CI", "CM", "CN", "DE", "EG", "ES", "ET", "FR", "GB", "GH", "HT", "IE", "IN", "KE", "MG", "MW", "MZ", "NG", "NL", "PH", "PK", "RW", "SN", "TZ", "UG", "US", "ZA", "ZM"]}, {"name": "transfer_ref", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "reference", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "bank_account_info", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "card_full_details", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "description", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "rates", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "payout_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "session_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "ledger_transaction_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "ledger_action_initiated_by", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "ledger_action_initiator_info", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "bank_account_deposit_details", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "transaction_status_description", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "reason_for_reversal", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "date", "data_type": "DATE", "is_categorical": false, "is_datetime": true, "description": ""}, {"name": "week", "data_type": "DATE", "is_categorical": false, "is_datetime": true, "description": ""}, {"name": "month", "data_type": "DATE", "is_categorical": false, "is_datetime": true, "description": ""}, {"name": "fee", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "otc_rate", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "processor_response_meta", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "promo_name", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "", "categorical_values": ["CHURNED_USER", "NEW_USER", "REGULATED_DEBIT", "UNREGULATED_DEBIT"]}, {"name": "promo_discount", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "tier_id", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "", "categorical_values": ["6516e5a6b5d8df9340ffac3b", "6516e5b4b5d8df9340ffac42", "66914c8bee6bfc188e7fba37", "669bac5798e8ad730201c38b", "66a0df9049d3c6d6b841fe01", "670567bb7949b84952d86117", "670567ee7949b84952d86219", "6705681d7949b84952d86230", "670568517949b84952d86244", "670568757949b84952d8624e", "67406a915b62d09b9813a94e", "67cc4545ce02680d78a20b24", "67cc473ace02680d78a20bd2", "67cc6bf4ce02680d78a23acc", "67cc715cce02680d78a24b67", "67cc7240ce02680d78a24cad", "67cc73f7ce02680d78a252b1", "67eed525f4a776c15583e8de", "67eed584f4a776c15583e9cc", "67eed5c4f4a776c15583e9dc", "67eed6a0f4a776c15583eb32", "67eed9dcf4a776c15583f1de", "67eeda77f4a776c15583f2c3", "67eedabbf4a776c15583f446", "67eedb14f4a776c15583f46a"]}, {"name": "deposit_transaction_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "channel", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "", "categorical_values": ["ACH_BANK_ACCOUNT", "ADMIN", "BANK_ACCOUNT", "CARD", "INTERAC", "INTERNAL", "MOBILE_MONEY", "RFP", "UPI", "VIRTUAL_BANK_ACCOUNT", "VIRTUAL_CARD", "WIDGET"]}, {"name": "from_to_usd", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": ""}, {"name": "to_to_usd", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": ""}], "sample_rows": "{\n  \"id\": \"f3a2b1c4d5e6f7g8h9i0j1k2l\",\n  \"created_at\": \"2025-06-15T10:20:30.123000\",\n  \"updated_at\": \"2025-06-20T12:45:50.456000\",\n  \"amount\": 50.0,\n  \"currency\": \"EUR\",\n  \"amount_usd\": 50.0,\n  \"fulfillment_asset_value\": 45000.75,\n  \"fulfillment_asset\": \"EUR\",\n  \"fulfillment_asset_value_usd\": null,\n  \"recipient_amt\": null,\n  \"recipient_currency\": null,\n  \"recipient_amount_usd\": null,\n  \"processor\": \"PAYPAL\",\n  \"transaction_type\": \"DEPOSIT\",\n  \"state\": \"COMPLETED\",\n  \"user_id\": \"a1b2c3d4e5f6g7h8i9j0k1l2\",\n  \"sender_id\": null,\n  \"source_country\": \"FR\",\n  \"receiver_id\": \"a1b2c3d4e5f6g7h8i9j0k1l2\",\n  \"recipient_country\": \"DE\",\n  \"transfer_ref\": \"*********\",\n  \"reference\": \"*********\",\n  \"bank_account_info\": \"{\\n  \\\"accountName\\\": \\\"JANE DOE\\\",\\n  \\\"accountNumber\\\": \\\"*********0\\\",\\n  \\\"accountPhone\\\": \\\"\\\",\\n  \\\"bankCode\\\": \\\"000123\\\",\\n  \\\"bankName\\\": \\\"Global Bank\\\",\\n  \\\"country\\\": \\\"DE\\\",\\n  \\\"currency\\\": \\\"EUR\\\",\\n  \\\"userAcknowledgedScamAlert\\\": false\\n}\",\n  \"card_full_details\": \"null\",\n  \"description\": null,\n  \"rates\": \"{\\n  \\\"EUR\\\": {\\n    \\\"EUR\\\": \\\"1\\\",\\n    \\\"USD\\\": \\\"1.1\\\"\\n  },\\n  \\\"USD\\\": {\\n    \\\"EUR\\\": \\\"0.**********\\\",\\n    \\\"USD\\\": \\\"1\\\"\\n  }\\n}\",\n  \"payout_id\": \"abcdef*********0abcdef12\",\n  \"session_id\": \"098765432109876543210987654321\",\n  \"ledger_transaction_id\": \"f3a2b1c4d5e6f7g8h9i0j1k2m\",\n  \"ledger_action_initiated_by\": \"abcdefabcdefabcdefabcdefab\",\n  \"ledger_action_initiator_info\": \"{\\n  \\\"email\\\": \\\"<EMAIL>\\\",\\n  \\\"name\\\": \\\"Example User\\\",\\n  \\\"phone\\\": \\\"+************\\\"\\n}\",\n  \"bank_account_deposit_details\": null,\n  \"transaction_status_description\": null,\n  \"reason_for_reversal\": null,\n  \"date\": \"2025-06-15\",\n  \"week\": \"2025-06-15\",\n  \"month\": \"2025-06-01\",\n  \"fee\": 0.0,\n  \"otc_rate\": null,\n  \"processor_response_meta\": \"{\\n  \\\"sessionId\\\": \\\"098765432109876543210987654321\\\",\\n  \\\"statusMessage\\\": \\\"SUCCESS\\\",\\n  \\\"transactionId\\\": *********,\\n  \\\"transactionReference\\\": \\\"Example-abcdef*********0abcdef12-*************\\\",\\n  \\\"transactionResponseCode\\\": 0,\\n  \\\"transactionType\\\": \\\"E2E\\\"\\n}\",\n  \"promo_name\": \"LIMITED_OFFER\",\n  \"promo_discount\": 0.025,\n  \"tier_id\": \"abcdefabcdefabcdefabcdefab\",\n  \"deposit_transaction_id\": \"f3a2b1c4d5e6f7g8h9i0j1k2n\",\n  \"channel\": \"CREDIT_CARD\",\n  \"from_to_usd\": null,\n  \"to_to_usd\": null\n}"}]}