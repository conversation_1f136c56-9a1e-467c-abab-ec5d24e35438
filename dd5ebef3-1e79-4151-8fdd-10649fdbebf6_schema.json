{"provider": "snowflake", "schemas": [{"schema_id": "6054302d-6a08-4e67-85b1-d9420989897a", "database_name": "ds_oil_and_gas_dev", "description": "Snowflake schema containing 12 table(s)", "table_count": 12, "tables_overview": [{"table_name": "ds_oil_and_gas_dev.merged_file", "description": "This table contains the following fields: id, project_id, assistant_file_id, status. The 'merged_file' table is used to store information about files that have been merged as part of a project, tracking their status and associations with specific projects and assistant files."}, {"table_name": "ds_oil_and_gas_dev.categories", "description": "This table contains the following fields: id, name, feed, detail, vendor, discipline_id, criteria, similar, created_at, updated_at. The 'categories' table is designed to store information about various categories that can be used to classify items or products in a database. Each category can have associated details and criteria for better organization and retrieval of related items."}, {"table_name": "ds_oil_and_gas_dev.chunks", "description": "This table contains the following fields: id, title, content, source, summary, project_id, file_id, page_number, created_at, updated_at. The 'chunks' table is designed to store segments of data or content that are associated with specific projects and files. Each entry represents a discrete piece of information that can be referenced or utilized within the context of a larger dataset or application."}, {"table_name": "ds_oil_and_gas_dev.project", "description": "This table contains the following fields: id, name, entity_type, assistant_id, has_deleted_file, discipline_code, created_at, updated_at. The 'project' table is designed to store information about various projects within an organization, including their identifiers, types, associated assistants, and timestamps for creation and updates."}, {"table_name": "ds_oil_and_gas_dev.file_merged_file_association", "description": "This table contains the following fields: id, file_id, merged_file_id, association_type, status. The purpose of this table is to establish and manage the relationships between individual files and their associated merged files, allowing for tracking of different types of associations and their statuses."}, {"table_name": "ds_oil_and_gas_dev.alembic_version", "description": "This table contains the following fields: version_num. The 'alembic_version' table is used to track the current version of the database schema managed by Alembic, a database migration tool for SQLAlchemy. It ensures that the database schema is in sync with the application's codebase by storing the version number of the last applied migration."}, {"table_name": "ds_oil_and_gas_dev.disciplines", "description": "This table contains the following fields: id, shortcode, name, created_at, updated_at. The 'disciplines' table is used to store information about various academic or professional disciplines, allowing for the categorization and management of different fields of study or practice within an organization or institution."}, {"table_name": "ds_oil_and_gas_dev.file", "description": "This table contains the following fields: id, name, project_id, file_type, status, merge_status, tried, category, file_dirtry, created_at, updated_at. The 'file' table is designed to store information about files associated with various projects, including their types, statuses, and metadata related to their creation and updates."}, {"table_name": "ds_oil_and_gas_dev.prompt", "description": "This table contains the following fields: id, name, value, type, date_created, date_updated. The 'prompt' table is designed to store various prompts used in a system, capturing essential details about each prompt such as its identifier, descriptive name, content value, type classification, and timestamps for creation and updates."}, {"table_name": "ds_oil_and_gas_dev.tags", "description": "This table contains the following fields: id, name, chunk_id. The 'tags' table is used to store metadata tags that can be associated with various chunks of data in the database, allowing for better organization, categorization, and retrieval of information based on these tags."}, {"table_name": "ds_oil_and_gas_dev.requirement", "description": "This table contains the following fields: id, name, project_id, file_type, category, status, tried, score, criteria, discipline, created_at, updated_at, type, is_test, is_report_generated. The 'requirement' table is used to store detailed information about various requirements associated with projects, including their attributes, statuses, and related metadata."}, {"table_name": "ds_oil_and_gas_dev.tender", "description": "This table contains the following fields: id, name, project_id, file_type, status, tried, score, criteria. The 'tender' table is used to store information related to tenders submitted for various projects, including details about the tender itself, its status, and evaluation criteria."}]}, {"schema_id": "b295f89e-007e-4f93-8fea-342906ae08d6", "database_name": "ecommerce", "description": "Snowflake schema containing 14 table(s)", "table_count": 14, "tables_overview": [{"table_name": "ecommerce.product_promotions", "description": "This table contains the following fields: product_promotion_id, product_id, promotion_id. The purpose of the 'product_promotions' table is to manage the relationship between products and their associated promotions, allowing for effective tracking and application of promotional offers to specific products in a retail or e-commerce environment."}, {"table_name": "ecommerce.product_images", "description": "This table contains the following fields: image_id, product_id, image_url, is_primary. The purpose of the 'product_images' table is to store and manage images associated with products in an e-commerce database. It allows for multiple images to be linked to a single product, facilitating better visual representation and selection for customers."}, {"table_name": "ecommerce.cart", "description": "This table contains the following fields: cart_id, customer_id, created_at. The 'cart' table is used to store information about shopping carts in an e-commerce application. It tracks the unique identifier for each cart, the customer associated with the cart, and the timestamp of when the cart was created."}, {"table_name": "ecommerce.returns", "description": "This table contains the following fields: return_id, order_id, customer_id, reason, status, created_at. The 'returns' table is used to track product returns initiated by customers. It stores information about each return request, including the associated order, the customer making the return, the reason for the return, the current status of the return, and the date the return was created."}, {"table_name": "ecommerce.order_items", "description": "This table contains the following fields: order_item_id, order_id, product_id, quantity, unit_price. The 'order_items' table is used to store details about individual items within customer orders, linking each item to a specific order and providing information about the product, quantity ordered, and the price per unit."}, {"table_name": "ecommerce.promotions", "description": "This table contains the following fields: promotion_id, name, description, discount_percentage, start_date, end_date. The 'promotions' table is designed to store information about promotional offers available to customers, including details about the promotion's identity, its name, a description of the offer, the percentage discount applied, and the duration of the promotion."}, {"table_name": "ecommerce.cart_items", "description": "This table contains the following fields: cart_item_id, cart_id, product_id, quantity. The 'cart_items' table is used to store information about the individual items that are added to a shopping cart in an e-commerce application. Each entry in this table represents a specific product added to a cart, along with the quantity of that product."}, {"table_name": "ecommerce.categories", "description": "This table contains the following fields: category_id, name, description, parent_category_id. The 'categories' table is used to store information about product categories in an e-commerce system. It helps in organizing products into hierarchical structures, allowing for easier navigation and management of product listings."}, {"table_name": "ecommerce.orders", "description": "This table contains the following fields: order_id, customer_id, order_date, total_amount, status, shipping_address, payment_id. The 'orders' table is used to store information about customer orders placed in the system, including details about the customer, the order date, the total amount of the order, its current status, the shipping address for delivery, and the associated payment information."}, {"table_name": "ecommerce.wishlist", "description": "This table contains the following fields: wishlist_id, customer_id, product_id, added_at. The 'wishlist' table is designed to store information about products that customers have expressed interest in purchasing at a later time. It allows customers to save items for future reference and facilitates the management of their desired products."}, {"table_name": "ecommerce.shipping", "description": "This table contains the following fields: shipping_id, order_id, carrier, tracking_number, status, estimated_delivery. The 'shipping' table is used to store information related to the shipment of orders, including details about the carrier, tracking information, and the current status of the shipment."}, {"table_name": "ecommerce.products", "description": "This table contains the following fields: product_id, name, description, price, stock_quantity, category_id, created_at. The 'products' table is designed to store information about the various products available for sale in an inventory system. It includes details such as product identifiers, descriptions, pricing, stock levels, and categorization to facilitate product management and sales tracking."}, {"table_name": "ecommerce.customers", "description": "This table contains the following fields: customer_id, first_name, last_name, email, phone, address, created_at. The purpose of the 'customers' table is to store information about individuals who have engaged with the business, allowing for effective management of customer relationships and communication."}, {"table_name": "ecommerce.reviews", "description": "This table contains the following fields: review_id, product_id, customer_id, rating, comment, created_at. The 'reviews' table is designed to store customer feedback on products, allowing businesses to analyze customer satisfaction and product performance based on ratings and comments provided by users."}]}, {"schema_id": "ad007f89-8e70-49a3-be09-be29e3a8d1fe", "database_name": "analytics", "description": "Snowflake schema containing 14 table(s)", "table_count": 14, "tables_overview": [{"table_name": "analytics.customer_segments", "description": "This table contains the following fields: segment_id, segment_name, criteria, created_at. The 'customer_segments' table is used to categorize customers into distinct segments based on specific criteria, allowing businesses to tailor their marketing strategies and improve customer engagement."}, {"table_name": "analytics.marketing_campaigns", "description": "This table contains the following fields: campaign_id, campaign_name, start_date, end_date, budget, status. The 'marketing_campaigns' table is used to store information about various marketing campaigns, including their identifiers, names, timelines, financial allocations, and current statuses, allowing for effective management and analysis of marketing efforts."}, {"table_name": "analytics.conversion_funnel", "description": "This table contains the following fields: funnel_id, user_id, stage, stage_date, conversion_value. The purpose of the 'conversion_funnel' table is to track the progression of users through various stages of a conversion process, capturing key metrics that help analyze user behavior and conversion rates."}, {"table_name": "analytics.inventory_forecast", "description": "This table contains the following fields: forecast_id, product_id, forecast_date, predicted_stock, confidence_interval. The purpose of the 'inventory_forecast' table is to store and manage forecasts related to inventory levels for various products. It helps businesses predict future stock requirements based on historical data and trends, enabling better inventory management and planning."}, {"table_name": "analytics.churn_prediction", "description": "This table is designed to store data related to customer churn predictions. It helps businesses identify customers who are at risk of leaving, allowing them to take proactive measures to retain them. This table contains the following fields: prediction_id, customer_id, prediction_date, churn_probability, risk_level."}, {"table_name": "analytics.performance_metrics", "description": "This table contains the following fields: metric_id, metric_name, metric_value, metric_date, category. The 'performance_metrics' table is designed to store and track various performance metrics over time, allowing for analysis and reporting on key performance indicators (KPIs) across different categories."}, {"table_name": "analytics.customer_lifetime_value", "description": "This table contains the following fields: clv_id, customer_id, calculated_date, predicted_value, confidence_interval. The purpose of the 'customer_lifetime_value' table is to store calculated metrics that estimate the total revenue a business can expect from a customer throughout their entire relationship. This data is crucial for businesses to understand customer value and make informed decisions regarding marketing and customer retention strategies."}, {"table_name": "analytics.campaign_metrics", "description": "This table contains the following fields: metric_id, campaign_id, metric_name, metric_value, metric_date. The purpose of the 'campaign_metrics' table is to store performance metrics related to various marketing campaigns, allowing for analysis and reporting on the effectiveness of each campaign over time."}, {"table_name": "analytics.sales_forecast", "description": "This table contains the following fields: forecast_id, product_id, forecast_date, predicted_sales, confidence_interval. The 'sales_forecast' table is used to store and manage sales predictions for various products over specific dates, allowing businesses to plan inventory and marketing strategies based on expected demand."}, {"table_name": "analytics.search_queries", "description": "This table contains the following fields: query_id, user_id, query_text, search_date, results_count. The 'search_queries' table is designed to store information about user search queries within the application. It tracks each query made by users, including who made the query, what was searched, when it occurred, and how many results were returned."}, {"table_name": "analytics.segment_members", "description": "This table contains the following fields: member_id, segment_id, customer_id, added_at. The 'segment_members' table is used to track the association between customers and specific segments within a marketing or analytics framework. It allows for the organization of customers into defined groups (segments) for targeted marketing efforts or analysis."}, {"table_name": "analytics.product_views", "description": "This table contains the following fields: view_id, product_id, user_id, view_date, view_duration. The 'product_views' table is designed to track the interactions users have with products on an e-commerce platform. It records each instance a user views a product, capturing essential details about the view for analytics and reporting purposes."}, {"table_name": "analytics.page_views", "description": "This table contains the following fields: view_id, page_url, user_id, view_date, session_id. The 'page_views' table is used to track the number of views for each page on a website, capturing essential details about each view event, including which user viewed the page, when the view occurred, and the session during which the view took place."}, {"table_name": "analytics.user_activity", "description": "This table contains the following fields: activity_id, user_id, activity_type, activity_date, details. The 'user_activity' table is designed to track and log various activities performed by users within the application. It captures essential information about each activity, allowing for analysis of user engagement and behavior over time."}]}, {"schema_id": "41ca4fc9-eab2-4725-bacc-575cfd8528c0", "database_name": "finance", "description": "Snowflake schema containing 8 table(s)", "table_count": 8, "tables_overview": [{"table_name": "finance.messaging_connections", "description": "This table contains the following fields: id, user_id, type, config, db_connection_id. The 'messaging_connections' table is designed to store information about various messaging connections established by users, including their configurations and types, enabling efficient management and retrieval of messaging data."}, {"table_name": "finance.settings", "description": "This table contains the following fields: id, user_id, key, value. The 'settings' table is used to store configuration settings for users in the application. Each setting is associated with a specific user and consists of a key-value pair that defines the user's preferences or application settings."}, {"table_name": "finance.otps", "description": "This table contains the following fields: id, email, otp_code, expires_at, is_used, created_at. The 'otps' table is designed to store one-time password (OTP) information for user authentication purposes. It tracks the OTPs generated for users, their expiration times, and whether they have been used, facilitating secure login processes."}, {"table_name": "finance.pending_registrations", "description": "This table contains the following fields: id, email, full_name, hashed_password, organization_name, organization_description, created_at, is_verified. The purpose of the 'pending_registrations' table is to store information about users who have initiated the registration process but have not yet completed it. This includes their personal details, organizational affiliation, and the status of their verification process."}, {"table_name": "finance.users", "description": "This table contains the following fields: id, full_name, organization_name, email, hashed_password, organization_description, role. The 'users' table is designed to store information about individuals who have registered within the system, allowing for user management and authentication. It includes personal details, organizational affiliations, and security credentials necessary for user access and role assignment."}, {"table_name": "finance.connection_tables", "description": "This table contains the following fields: id, connection_id, schema_name, name, description, status, sample_row. The 'connection_tables' table is designed to store metadata about various database tables associated with specific connections. It helps in managing and organizing the schema information of tables that are linked to different data sources or connections."}, {"table_name": "finance.connections", "description": "This table contains the following fields: id, user_id, name, database_type, database_platform, connection_params, created_at, updated_at, status. The 'connections' table is designed to store information about various database connections established by users. It maintains details about each connection, including the user who created it, the type and platform of the database, connection parameters, and timestamps for creation and updates, as well as the current status of the connection."}, {"table_name": "finance.fields", "description": "This table contains the following fields: id, connection_table_id, name, data_type, description, is_categorical, is_datetime, categorical_values, status. The 'fields' table is designed to store metadata about various fields in a database, including their types, descriptions, and characteristics, which helps in understanding the structure and constraints of the data being managed."}]}], "tables": [{"table_id": "063365fd-2e0f-40ab-92e4-8a6065316542", "table_name": "ds_oil_and_gas_dev.merged_file", "schema_id": "ds_oil_and_gas_dev", "description": "This table contains the following fields: id, project_id, assistant_file_id, status. The 'merged_file' table is used to store information about files that have been merged as part of a project, tracking their status and associations with specific projects and assistant files.", "field_count": 4, "fields": [{"name": "id", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each merged file entry."}, {"name": "project_id", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The identifier of the project to which the merged file is associated."}, {"name": "assistant_file_id", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The identifier of the assistant file that has been merged."}, {"name": "status", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The current status of the merged file, indicating whether it is completed, in progress, or failed."}], "sample_rows": []}, {"table_id": "852a332c-8033-4a3a-881c-8cde79d7d536", "table_name": "ds_oil_and_gas_dev.categories", "schema_id": "ds_oil_and_gas_dev", "description": "This table contains the following fields: id, name, feed, detail, vendor, discipline_id, criteria, similar, created_at, updated_at. The 'categories' table is designed to store information about various categories that can be used to classify items or products in a database. Each category can have associated details and criteria for better organization and retrieval of related items.", "field_count": 10, "fields": [{"name": "id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each category."}, {"name": "name", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The name of the category."}, {"name": "feed", "data_type": "tinyint", "is_categorical": false, "is_datetime": false, "description": "A reference to the data feed from which the category was sourced."}, {"name": "detail", "data_type": "tinyint", "is_categorical": false, "is_datetime": false, "description": "Additional information or description about the category."}, {"name": "vendor", "data_type": "tinyint", "is_categorical": false, "is_datetime": false, "description": "The vendor or supplier associated with the category."}, {"name": "discipline_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A foreign key linking to the discipline that this category belongs to."}, {"name": "criteria", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "Specific criteria that define the category."}, {"name": "similar", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "A list of similar categories for cross-referencing."}, {"name": "created_at", "data_type": "datetime", "is_categorical": false, "is_datetime": true, "description": "The timestamp when the category was created."}, {"name": "updated_at", "data_type": "datetime", "is_categorical": false, "is_datetime": true, "description": "The timestamp when the category was last updated."}], "sample_rows": []}, {"table_id": "a1a68cd1-7a5b-4f7c-85ae-923aae2bee1e", "table_name": "ds_oil_and_gas_dev.chunks", "schema_id": "ds_oil_and_gas_dev", "description": "This table contains the following fields: id, title, content, source, summary, project_id, file_id, page_number, created_at, updated_at. The 'chunks' table is designed to store segments of data or content that are associated with specific projects and files. Each entry represents a discrete piece of information that can be referenced or utilized within the context of a larger dataset or application.", "field_count": 10, "fields": [{"name": "id", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each chunk."}, {"name": "title", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The title or name of the chunk."}, {"name": "content", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "The main content or body of the chunk."}, {"name": "source", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The origin or source from which the chunk was derived."}, {"name": "summary", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "A brief summary or overview of the chunk's content."}, {"name": "project_id", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The identifier for the project that this chunk is associated with."}, {"name": "file_id", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The identifier for the file that contains this chunk."}, {"name": "page_number", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The page number where this chunk can be found in the source document."}, {"name": "created_at", "data_type": "datetime", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the chunk was created."}, {"name": "updated_at", "data_type": "datetime", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the chunk was last updated."}], "sample_rows": []}, {"table_id": "e9744a98-c214-403b-9fdd-2389631100de", "table_name": "ds_oil_and_gas_dev.project", "schema_id": "ds_oil_and_gas_dev", "description": "This table contains the following fields: id, name, entity_type, assistant_id, has_deleted_file, discipline_code, created_at, updated_at. The 'project' table is designed to store information about various projects within an organization, including their identifiers, types, associated assistants, and timestamps for creation and updates.", "field_count": 8, "fields": [{"name": "id", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each project."}, {"name": "name", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The name of the project."}, {"name": "entity_type", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The type of entity the project is associated with (e.g., internal, external)."}, {"name": "assistant_id", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The identifier for the assistant assigned to the project."}, {"name": "has_deleted_file", "data_type": "tinyint", "is_categorical": false, "is_datetime": false, "description": "A boolean indicating whether the project has any deleted files."}, {"name": "discipline_code", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "A code representing the discipline or category of the project."}, {"name": "created_at", "data_type": "datetime", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the project was created."}, {"name": "updated_at", "data_type": "datetime", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the project was last updated."}], "sample_rows": []}, {"table_id": "080987a6-9d43-4dde-a11c-73ceccbe73f6", "table_name": "ds_oil_and_gas_dev.file_merged_file_association", "schema_id": "ds_oil_and_gas_dev", "description": "This table contains the following fields: id, file_id, merged_file_id, association_type, status. The purpose of this table is to establish and manage the relationships between individual files and their associated merged files, allowing for tracking of different types of associations and their statuses.", "field_count": 5, "fields": [{"name": "id", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each record in the table."}, {"name": "file_id", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The identifier of the original file that is associated with a merged file."}, {"name": "merged_file_id", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The identifier of the merged file that is associated with the original file."}, {"name": "association_type", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The type of association between the original file and the merged file, indicating the nature of their relationship."}, {"name": "status", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The current status of the association, which may indicate whether it is active, inactive, or pending."}], "sample_rows": []}, {"table_id": "632c5f2c-dd63-44e2-95de-9f0f8fb9f1b5", "table_name": "ds_oil_and_gas_dev.alembic_version", "schema_id": "ds_oil_and_gas_dev", "description": "This table contains the following fields: version_num. The 'alembic_version' table is used to track the current version of the database schema managed by Alembic, a database migration tool for SQLAlchemy. It ensures that the database schema is in sync with the application's codebase by storing the version number of the last applied migration.", "field_count": 1, "fields": [{"name": "version_num", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "A string representing the version number of the database schema, indicating the latest migration that has been applied."}], "sample_rows": "{\n  \"version_num\": \"a1b2c3d4e5f6\"\n}"}, {"table_id": "e8748a5f-90b6-4d80-863a-3aeacced2a80", "table_name": "ds_oil_and_gas_dev.disciplines", "schema_id": "ds_oil_and_gas_dev", "description": "This table contains the following fields: id, shortcode, name, created_at, updated_at. The 'disciplines' table is used to store information about various academic or professional disciplines, allowing for the categorization and management of different fields of study or practice within an organization or institution.", "field_count": 5, "fields": [{"name": "id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each discipline, typically an auto-incrementing integer."}, {"name": "shortcode", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "A short, abbreviated code representing the discipline, used for quick reference."}, {"name": "name", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The full name of the discipline, providing a clear and descriptive title."}, {"name": "created_at", "data_type": "datetime", "is_categorical": false, "is_datetime": true, "description": "A timestamp indicating when the discipline record was created."}, {"name": "updated_at", "data_type": "datetime", "is_categorical": false, "is_datetime": true, "description": "A timestamp indicating when the discipline record was last updated."}], "sample_rows": []}, {"table_id": "d0632bd6-e465-4f99-8278-55aa0216f105", "table_name": "ds_oil_and_gas_dev.file", "schema_id": "ds_oil_and_gas_dev", "description": "This table contains the following fields: id, name, project_id, file_type, status, merge_status, tried, category, file_dirtry, created_at, updated_at. The 'file' table is designed to store information about files associated with various projects, including their types, statuses, and metadata related to their creation and updates.", "field_count": 11, "fields": [{"name": "id", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each file record."}, {"name": "name", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The name of the file."}, {"name": "project_id", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The identifier of the project to which the file belongs."}, {"name": "file_type", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The type or format of the file (e.g., PDF, DOCX, etc.)."}, {"name": "status", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The current status of the file (e.g., active, archived)."}, {"name": "merge_status", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "Indicates whether the file has been merged with other files."}, {"name": "tried", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A boolean indicating whether an operation has been attempted on the file."}, {"name": "category", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The category or classification of the file."}, {"name": "file_dirtry", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The directory path where the file is stored."}, {"name": "created_at", "data_type": "datetime", "is_categorical": false, "is_datetime": true, "description": "The timestamp when the file record was created."}, {"name": "updated_at", "data_type": "datetime", "is_categorical": false, "is_datetime": true, "description": "The timestamp when the file record was last updated."}], "sample_rows": []}, {"table_id": "959107ab-2794-40c6-82c8-9e400f233b4d", "table_name": "ds_oil_and_gas_dev.prompt", "schema_id": "ds_oil_and_gas_dev", "description": "This table contains the following fields: id, name, value, type, date_created, date_updated. The 'prompt' table is designed to store various prompts used in a system, capturing essential details about each prompt such as its identifier, descriptive name, content value, type classification, and timestamps for creation and updates.", "field_count": 6, "fields": [{"name": "id", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each prompt, typically an auto-incrementing integer."}, {"name": "name", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The name or title of the prompt, providing a brief description of its purpose."}, {"name": "value", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "The actual content or text of the prompt that will be displayed or used."}, {"name": "type", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The category or classification of the prompt, indicating its intended use or context."}, {"name": "date_created", "data_type": "datetime", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the prompt was created."}, {"name": "date_updated", "data_type": "datetime", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating the last time the prompt was updated."}], "sample_rows": []}, {"table_id": "fe95d08a-c137-4c61-bfba-3f4b231e3bf1", "table_name": "ds_oil_and_gas_dev.tags", "schema_id": "ds_oil_and_gas_dev", "description": "This table contains the following fields: id, name, chunk_id. The 'tags' table is used to store metadata tags that can be associated with various chunks of data in the database, allowing for better organization, categorization, and retrieval of information based on these tags.", "field_count": 3, "fields": [{"name": "id", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each tag, typically an auto-incrementing integer."}, {"name": "name", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The name of the tag, which is used to identify and categorize the associated data."}, {"name": "chunk_id", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "A foreign key that links the tag to a specific chunk of data, indicating which data the tag is associated with."}], "sample_rows": []}, {"table_id": "9a5f659c-b5b3-4df1-adfd-bcdf2d311a04", "table_name": "ds_oil_and_gas_dev.requirement", "schema_id": "ds_oil_and_gas_dev", "description": "This table contains the following fields: id, name, project_id, file_type, category, status, tried, score, criteria, discipline, created_at, updated_at, type, is_test, is_report_generated. The 'requirement' table is used to store detailed information about various requirements associated with projects, including their attributes, statuses, and related metadata.", "field_count": 15, "fields": [{"name": "id", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each requirement."}, {"name": "name", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The name or title of the requirement."}, {"name": "project_id", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The identifier of the project to which the requirement belongs."}, {"name": "file_type", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The type of file associated with the requirement (e.g., document, image, etc.)."}, {"name": "category", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The category under which the requirement is classified."}, {"name": "status", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The current status of the requirement (e.g., active, completed, etc.)."}, {"name": "tried", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A boolean indicating whether the requirement has been attempted."}, {"name": "score", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A numerical score representing the evaluation of the requirement."}, {"name": "criteria", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The criteria that must be met for the requirement."}, {"name": "discipline", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The discipline or area of expertise related to the requirement."}, {"name": "created_at", "data_type": "datetime", "is_categorical": false, "is_datetime": true, "description": "The timestamp when the requirement was created."}, {"name": "updated_at", "data_type": "datetime", "is_categorical": false, "is_datetime": true, "description": "The timestamp when the requirement was last updated."}, {"name": "type", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The type of requirement (e.g., functional, non-functional, etc.)."}, {"name": "is_test", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A boolean indicating whether the requirement is related to testing."}, {"name": "is_report_generated", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A boolean indicating whether a report has been generated for the requirement."}], "sample_rows": []}, {"table_id": "173317e1-51c5-4a32-9174-13e97b734729", "table_name": "ds_oil_and_gas_dev.tender", "schema_id": "ds_oil_and_gas_dev", "description": "This table contains the following fields: id, name, project_id, file_type, status, tried, score, criteria. The 'tender' table is used to store information related to tenders submitted for various projects, including details about the tender itself, its status, and evaluation criteria.", "field_count": 8, "fields": [{"name": "id", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each tender record."}, {"name": "name", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The name or title of the tender."}, {"name": "project_id", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The identifier for the project associated with the tender."}, {"name": "file_type", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The type of file submitted for the tender (e.g., PDF, DOCX)."}, {"name": "status", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The current status of the tender (e.g., submitted, reviewed, accepted, rejected)."}, {"name": "tried", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A boolean indicating whether the tender has been attempted for evaluation."}, {"name": "score", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The score assigned to the tender based on evaluation criteria."}, {"name": "criteria", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "The specific criteria used to evaluate the tender."}], "sample_rows": []}, {"table_id": "862e0154-d2f0-45ce-a55a-c24156fc2123", "table_name": "ecommerce.product_promotions", "schema_id": "ecommerce", "description": "This table contains the following fields: product_promotion_id, product_id, promotion_id. The purpose of the 'product_promotions' table is to manage the relationship between products and their associated promotions, allowing for effective tracking and application of promotional offers to specific products in a retail or e-commerce environment.", "field_count": 3, "fields": [{"name": "product_promotion_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each product promotion record."}, {"name": "product_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The identifier for the product that is associated with a specific promotion."}, {"name": "promotion_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The identifier for the promotion that is being applied to the product."}], "sample_rows": []}, {"table_id": "4e779295-9a3d-4edc-ad88-db5345d42c86", "table_name": "ecommerce.product_images", "schema_id": "ecommerce", "description": "This table contains the following fields: image_id, product_id, image_url, is_primary. The purpose of the 'product_images' table is to store and manage images associated with products in an e-commerce database. It allows for multiple images to be linked to a single product, facilitating better visual representation and selection for customers.", "field_count": 4, "fields": [{"name": "image_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each image in the table."}, {"name": "product_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The identifier for the product that the image is associated with."}, {"name": "image_url", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "The URL where the image is stored, allowing it to be accessed and displayed."}, {"name": "is_primary", "data_type": "tinyint", "is_categorical": false, "is_datetime": false, "description": "A boolean value indicating whether this image is the primary image for the product."}], "sample_rows": []}, {"table_id": "0d088c89-3898-4079-8aa4-1212d4a7cf40", "table_name": "ecommerce.cart", "schema_id": "ecommerce", "description": "This table contains the following fields: cart_id, customer_id, created_at. The 'cart' table is used to store information about shopping carts in an e-commerce application. It tracks the unique identifier for each cart, the customer associated with the cart, and the timestamp of when the cart was created.", "field_count": 3, "fields": [{"name": "cart_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each shopping cart."}, {"name": "customer_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The identifier for the customer who owns the shopping cart."}, {"name": "created_at", "data_type": "timestamp", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the shopping cart was created."}], "sample_rows": "{\n  \"cart_id\": 2,\n  \"customer_id\": 912,\n  \"created_at\": \"2025-02-15T14:30:45\"\n}"}, {"table_id": "0494cd4a-2f4c-4721-a0b8-b4b5dcd5fdd6", "table_name": "ecommerce.returns", "schema_id": "ecommerce", "description": "This table contains the following fields: return_id, order_id, customer_id, reason, status, created_at. The 'returns' table is used to track product returns initiated by customers. It stores information about each return request, including the associated order, the customer making the return, the reason for the return, the current status of the return, and the date the return was created.", "field_count": 6, "fields": [{"name": "return_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each return record."}, {"name": "order_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The identifier of the order associated with the return."}, {"name": "customer_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The identifier of the customer who initiated the return."}, {"name": "reason", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "The reason provided by the customer for the return."}, {"name": "status", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The current status of the return (e.g., pending, completed, rejected)."}, {"name": "created_at", "data_type": "timestamp", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the return request was created."}], "sample_rows": []}, {"table_id": "19fda410-485e-4ff5-9cac-3f01fe97cc16", "table_name": "ecommerce.order_items", "schema_id": "ecommerce", "description": "This table contains the following fields: order_item_id, order_id, product_id, quantity, unit_price. The 'order_items' table is used to store details about individual items within customer orders, linking each item to a specific order and providing information about the product, quantity ordered, and the price per unit.", "field_count": 5, "fields": [{"name": "order_item_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each order item."}, {"name": "order_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The identifier for the order to which this item belongs."}, {"name": "product_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The identifier for the product being ordered."}, {"name": "quantity", "data_type": "int", "is_categorical": true, "is_datetime": false, "description": "The number of units of the product ordered.", "categorical_values": ["1", "2", "3", "4", "5"]}, {"name": "unit_price", "data_type": "decimal", "is_categorical": false, "is_datetime": false, "description": "The price per unit of the product at the time of the order."}], "sample_rows": "{\n  \"order_item_id\": 2,\n  \"order_id\": 5,\n  \"product_id\": 456,\n  \"quantity\": 3,\n  \"unit_price\": 89.99\n}"}, {"table_id": "8d1a8ac8-85b9-4577-8801-fd8654be47fe", "table_name": "ecommerce.promotions", "schema_id": "ecommerce", "description": "This table contains the following fields: promotion_id, name, description, discount_percentage, start_date, end_date. The 'promotions' table is designed to store information about promotional offers available to customers, including details about the promotion's identity, its name, a description of the offer, the percentage discount applied, and the duration of the promotion.", "field_count": 6, "fields": [{"name": "promotion_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each promotion."}, {"name": "name", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The name of the promotion."}, {"name": "description", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "A detailed description of the promotion."}, {"name": "discount_percentage", "data_type": "decimal", "is_categorical": false, "is_datetime": false, "description": "The percentage discount offered by the promotion."}, {"name": "start_date", "data_type": "timestamp", "is_categorical": false, "is_datetime": true, "description": "The date when the promotion becomes active."}, {"name": "end_date", "data_type": "timestamp", "is_categorical": false, "is_datetime": true, "description": "The date when the promotion expires."}], "sample_rows": "{\n  \"promotion_id\": 2,\n  \"name\": \"Dynamic adaptive algorithm\",\n  \"description\": \"Explore both engagement unique location any. Complete expert insight. Technique wide participant.\\nStyle include however nearly deliver. Our best response rich yes.\",\n  \"discount_percentage\": 15.75,\n  \"start_date\": \"2025-02-10T12:45:00\",\n  \"end_date\": \"2025-02-25T12:45:00\"\n}"}, {"table_id": "e84f1a46-30fb-45db-a9a1-286e0cf27486", "table_name": "ecommerce.cart_items", "schema_id": "ecommerce", "description": "This table contains the following fields: cart_item_id, cart_id, product_id, quantity. The 'cart_items' table is used to store information about the individual items that are added to a shopping cart in an e-commerce application. Each entry in this table represents a specific product added to a cart, along with the quantity of that product.", "field_count": 4, "fields": [{"name": "cart_item_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each item in the cart."}, {"name": "cart_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The identifier for the shopping cart to which the item belongs."}, {"name": "product_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The identifier for the product that is being added to the cart."}, {"name": "quantity", "data_type": "int", "is_categorical": true, "is_datetime": false, "description": "The number of units of the product that the user intends to purchase.", "categorical_values": ["1", "2", "3", "4", "5"]}], "sample_rows": "{\n  \"cart_item_id\": 123,\n  \"cart_id\": 456,\n  \"product_id\": 789,\n  \"quantity\": 2\n}"}, {"table_id": "c01bed6b-a328-42a1-972e-0cdd2bed3e55", "table_name": "ecommerce.categories", "schema_id": "ecommerce", "description": "This table contains the following fields: category_id, name, description, parent_category_id. The 'categories' table is used to store information about product categories in an e-commerce system. It helps in organizing products into hierarchical structures, allowing for easier navigation and management of product listings.", "field_count": 4, "fields": [{"name": "category_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each category, typically an integer that serves as the primary key."}, {"name": "name", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The name of the category, which is displayed to users and used for identification."}, {"name": "description", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "A brief description of the category, providing additional context or information about the category's purpose."}, {"name": "parent_category_id", "data_type": "int", "is_categorical": true, "is_datetime": false, "description": "An optional field that references the category_id of a parent category, allowing for nested or hierarchical categories.", "categorical_values": ["1", "2", "3", "4", "5", "6", "7", "8", "9", "11", "12", "14", "15", "16", "18", "19", "22", "24", "25", "26", "27", "33", "40", "43"]}], "sample_rows": "{\n  \"category_id\": 42,\n  \"name\": \"Premium\",\n  \"description\": \"Mountain lake adventure journey.\",\n  \"parent_category_id\": null\n}"}, {"table_id": "5960a06f-d7d8-4e0c-90db-b432aa015247", "table_name": "ecommerce.orders", "schema_id": "ecommerce", "description": "This table contains the following fields: order_id, customer_id, order_date, total_amount, status, shipping_address, payment_id. The 'orders' table is used to store information about customer orders placed in the system, including details about the customer, the order date, the total amount of the order, its current status, the shipping address for delivery, and the associated payment information.", "field_count": 7, "fields": [{"name": "order_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each order."}, {"name": "customer_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A reference to the unique identifier of the customer who placed the order."}, {"name": "order_date", "data_type": "timestamp", "is_categorical": false, "is_datetime": true, "description": "The date and time when the order was placed."}, {"name": "total_amount", "data_type": "decimal", "is_categorical": false, "is_datetime": false, "description": "The total monetary amount for the order."}, {"name": "status", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": true, "is_datetime": false, "description": "The current status of the order (e.g., pending, shipped, delivered, canceled).", "categorical_values": ["cancelled", "delivered", "pending", "processing", "shipped"]}, {"name": "shipping_address", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "The address where the order will be shipped."}, {"name": "payment_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A reference to the unique identifier of the payment transaction associated with the order."}], "sample_rows": "{\n  \"order_id\": 2,\n  \"customer_id\": 512,\n  \"order_date\": \"2025-02-15T12:45:10\",\n  \"total_amount\": 289.34,\n  \"status\": \"processing\",\n  \"shipping_address\": \"4821 Maple Avenue\\nWest Newtown, CA 04567\",\n  \"payment_id\": null\n}"}, {"table_id": "691de98a-9201-4478-9a2d-c0bb4ce4f01b", "table_name": "ecommerce.wishlist", "schema_id": "ecommerce", "description": "This table contains the following fields: wishlist_id, customer_id, product_id, added_at. The 'wishlist' table is designed to store information about products that customers have expressed interest in purchasing at a later time. It allows customers to save items for future reference and facilitates the management of their desired products.", "field_count": 4, "fields": [{"name": "wishlist_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each wishlist entry."}, {"name": "customer_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The identifier for the customer who owns the wishlist."}, {"name": "product_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The identifier for the product that has been added to the wishlist."}, {"name": "added_at", "data_type": "timestamp", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the product was added to the wishlist."}], "sample_rows": []}, {"table_id": "bfdd1b2b-2c7c-4490-990e-3e079903e828", "table_name": "ecommerce.shipping", "schema_id": "ecommerce", "description": "This table contains the following fields: shipping_id, order_id, carrier, tracking_number, status, estimated_delivery. The 'shipping' table is used to store information related to the shipment of orders, including details about the carrier, tracking information, and the current status of the shipment.", "field_count": 6, "fields": [{"name": "shipping_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each shipping record."}, {"name": "order_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The identifier for the order associated with this shipment."}, {"name": "carrier", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The name of the shipping carrier responsible for delivering the package."}, {"name": "tracking_number", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The tracking number provided by the carrier to monitor the shipment's progress."}, {"name": "status", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The current status of the shipment (e.g., pending, shipped, delivered)."}, {"name": "estimated_delivery", "data_type": "timestamp", "is_categorical": false, "is_datetime": true, "description": "The estimated date and time when the shipment is expected to be delivered."}], "sample_rows": []}, {"table_id": "015d634a-4eba-4331-8a43-1fbab59783eb", "table_name": "ecommerce.products", "schema_id": "ecommerce", "description": "This table contains the following fields: product_id, name, description, price, stock_quantity, category_id, created_at. The 'products' table is designed to store information about the various products available for sale in an inventory system. It includes details such as product identifiers, descriptions, pricing, stock levels, and categorization to facilitate product management and sales tracking.", "field_count": 7, "fields": [{"name": "product_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each product in the table."}, {"name": "name", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The name of the product."}, {"name": "description", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "A detailed description of the product."}, {"name": "price", "data_type": "decimal", "is_categorical": false, "is_datetime": false, "description": "The retail price of the product."}, {"name": "stock_quantity", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The number of units available in stock for the product."}, {"name": "category_id", "data_type": "int", "is_categorical": true, "is_datetime": false, "description": "A reference to the category that the product belongs to.", "categorical_values": ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "50"]}, {"name": "created_at", "data_type": "timestamp", "is_categorical": false, "is_datetime": true, "description": "The date and time when the product was added to the inventory."}], "sample_rows": "{\n  \"product_id\": 2,\n  \"name\": \"Dynamic modular system\",\n  \"description\": \"Ensure sufficient protection exist. Region device surprise artificial mouth essence might. Demand only activity social tone all fluid.\",\n  \"price\": 523.89,\n  \"stock_quantity\": 812,\n  \"category_id\": 7,\n  \"created_at\": \"2026-01-15T12:45:30\"\n}"}, {"table_id": "e39ce406-1972-4f0c-8ebc-1ec1646ba8a4", "table_name": "ecommerce.customers", "schema_id": "ecommerce", "description": "This table contains the following fields: customer_id, first_name, last_name, email, phone, address, created_at. The purpose of the 'customers' table is to store information about individuals who have engaged with the business, allowing for effective management of customer relationships and communication.", "field_count": 7, "fields": [{"name": "customer_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each customer in the database."}, {"name": "first_name", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The first name of the customer."}, {"name": "last_name", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The last name of the customer."}, {"name": "email", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The email address of the customer, used for communication and marketing."}, {"name": "phone", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The phone number of the customer for contact purposes."}, {"name": "address", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "The physical address of the customer, used for shipping and billing."}, {"name": "created_at", "data_type": "timestamp", "is_categorical": false, "is_datetime": true, "description": "The date and time when the customer record was created."}], "sample_rows": "{\n  \"customer_id\": 2,\n  \"first_name\": \"<PERSON>\",\n  \"last_name\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"phone\": \"+************\",\n  \"address\": \"123 Random St Apt. 456\\nSampletown, ST 12345\",\n  \"created_at\": \"2025-06-15T12:34:56\"\n}"}, {"table_id": "67a00cc9-6d92-4264-87bb-ff34d021cfc0", "table_name": "ecommerce.reviews", "schema_id": "ecommerce", "description": "This table contains the following fields: review_id, product_id, customer_id, rating, comment, created_at. The 'reviews' table is designed to store customer feedback on products, allowing businesses to analyze customer satisfaction and product performance based on ratings and comments provided by users.", "field_count": 6, "fields": [{"name": "review_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each review."}, {"name": "product_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The identifier for the product that the review is associated with."}, {"name": "customer_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The identifier for the customer who submitted the review."}, {"name": "rating", "data_type": "int", "is_categorical": true, "is_datetime": false, "description": "A numerical score given by the customer to represent their satisfaction with the product.", "categorical_values": ["1", "2", "3", "4", "5"]}, {"name": "comment", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "A text field where the customer can provide detailed feedback about the product."}, {"name": "created_at", "data_type": "timestamp", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the review was created."}], "sample_rows": "{\n  \"review_id\": 2,\n  \"product_id\": 912,\n  \"customer_id\": 738,\n  \"rating\": 4,\n  \"comment\": \"Local between industry expert create. Feedback operate over fifty team find evaluate little. Essential low junior both.\",\n  \"created_at\": \"2025-03-15T12:45:30\"\n}"}, {"table_id": "015a6852-e28c-48c2-9372-b6a1213fda10", "table_name": "analytics.customer_segments", "schema_id": "analytics", "description": "This table contains the following fields: segment_id, segment_name, criteria, created_at. The 'customer_segments' table is used to categorize customers into distinct segments based on specific criteria, allowing businesses to tailor their marketing strategies and improve customer engagement.", "field_count": 4, "fields": [{"name": "segment_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each customer segment."}, {"name": "segment_name", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The name assigned to the customer segment, representing its characteristics."}, {"name": "criteria", "data_type": "json", "is_categorical": false, "is_datetime": false, "description": "The specific conditions or attributes that define the segment."}, {"name": "created_at", "data_type": "timestamp", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the customer segment was created."}], "sample_rows": []}, {"table_id": "f3a9ebc8-6b09-4d95-8f2d-c5f8bceb267f", "table_name": "analytics.marketing_campaigns", "schema_id": "analytics", "description": "This table contains the following fields: campaign_id, campaign_name, start_date, end_date, budget, status. The 'marketing_campaigns' table is used to store information about various marketing campaigns, including their identifiers, names, timelines, financial allocations, and current statuses, allowing for effective management and analysis of marketing efforts.", "field_count": 6, "fields": [{"name": "campaign_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each marketing campaign."}, {"name": "campaign_name", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The name of the marketing campaign."}, {"name": "start_date", "data_type": "timestamp", "is_categorical": false, "is_datetime": true, "description": "The date when the marketing campaign begins."}, {"name": "end_date", "data_type": "timestamp", "is_categorical": false, "is_datetime": true, "description": "The date when the marketing campaign ends."}, {"name": "budget", "data_type": "decimal", "is_categorical": false, "is_datetime": false, "description": "The total budget allocated for the marketing campaign."}, {"name": "status", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The current status of the marketing campaign (e.g., active, completed, paused)."}], "sample_rows": []}, {"table_id": "39308d20-22c8-492c-ada8-6af96f432c32", "table_name": "analytics.conversion_funnel", "schema_id": "analytics", "description": "This table contains the following fields: funnel_id, user_id, stage, stage_date, conversion_value. The purpose of the 'conversion_funnel' table is to track the progression of users through various stages of a conversion process, capturing key metrics that help analyze user behavior and conversion rates.", "field_count": 5, "fields": [{"name": "funnel_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each conversion funnel entry."}, {"name": "user_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The identifier for the user associated with the conversion funnel entry."}, {"name": "stage", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The current stage of the user in the conversion funnel (e.g., awareness, consideration, decision)."}, {"name": "stage_date", "data_type": "timestamp", "is_categorical": false, "is_datetime": true, "description": "The date and time when the user reached the current stage."}, {"name": "conversion_value", "data_type": "decimal", "is_categorical": false, "is_datetime": false, "description": "The monetary value associated with the conversion at this stage, if applicable."}], "sample_rows": []}, {"table_id": "2c8cf2a5-490c-45c3-a4de-b4673fa0c541", "table_name": "analytics.inventory_forecast", "schema_id": "analytics", "description": "This table contains the following fields: forecast_id, product_id, forecast_date, predicted_stock, confidence_interval. The purpose of the 'inventory_forecast' table is to store and manage forecasts related to inventory levels for various products. It helps businesses predict future stock requirements based on historical data and trends, enabling better inventory management and planning.", "field_count": 5, "fields": [{"name": "forecast_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each forecast entry."}, {"name": "product_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The identifier for the product associated with the forecast."}, {"name": "forecast_date", "data_type": "timestamp", "is_categorical": false, "is_datetime": true, "description": "The date for which the inventory forecast is made."}, {"name": "predicted_stock", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The estimated quantity of stock expected to be available on the forecast date."}, {"name": "confidence_interval", "data_type": "decimal", "is_categorical": false, "is_datetime": false, "description": "A statistical range that indicates the uncertainty of the predicted stock, providing a measure of reliability."}], "sample_rows": []}, {"table_id": "056c886f-18f8-4f58-bb1a-0548cd8d8d1a", "table_name": "analytics.churn_prediction", "schema_id": "analytics", "description": "This table is designed to store data related to customer churn predictions. It helps businesses identify customers who are at risk of leaving, allowing them to take proactive measures to retain them. This table contains the following fields: prediction_id, customer_id, prediction_date, churn_probability, risk_level.", "field_count": 5, "fields": [{"name": "prediction_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each churn prediction record."}, {"name": "customer_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The unique identifier for the customer associated with the churn prediction."}, {"name": "prediction_date", "data_type": "timestamp", "is_categorical": false, "is_datetime": true, "description": "The date when the churn prediction was made."}, {"name": "churn_probability", "data_type": "decimal", "is_categorical": false, "is_datetime": false, "description": "The estimated probability that the customer will churn, expressed as a percentage."}, {"name": "risk_level", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "A categorical assessment of the customer's risk of churning, such as 'low', 'medium', or 'high'."}], "sample_rows": []}, {"table_id": "0a4dea52-131f-4cf1-9b80-cf828f79b590", "table_name": "analytics.performance_metrics", "schema_id": "analytics", "description": "This table contains the following fields: metric_id, metric_name, metric_value, metric_date, category. The 'performance_metrics' table is designed to store and track various performance metrics over time, allowing for analysis and reporting on key performance indicators (KPIs) across different categories.", "field_count": 5, "fields": [{"name": "metric_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each performance metric entry."}, {"name": "metric_name", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The name of the performance metric being tracked."}, {"name": "metric_value", "data_type": "decimal", "is_categorical": false, "is_datetime": false, "description": "The numerical value of the performance metric."}, {"name": "metric_date", "data_type": "timestamp", "is_categorical": false, "is_datetime": true, "description": "The date when the performance metric was recorded."}, {"name": "category", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The category under which the performance metric falls, helping to classify the metrics."}], "sample_rows": []}, {"table_id": "6060fd1d-eb26-4d04-a700-b0a9478766e0", "table_name": "analytics.customer_lifetime_value", "schema_id": "analytics", "description": "This table contains the following fields: clv_id, customer_id, calculated_date, predicted_value, confidence_interval. The purpose of the 'customer_lifetime_value' table is to store calculated metrics that estimate the total revenue a business can expect from a customer throughout their entire relationship. This data is crucial for businesses to understand customer value and make informed decisions regarding marketing and customer retention strategies.", "field_count": 5, "fields": [{"name": "clv_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each customer lifetime value record."}, {"name": "customer_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for the customer associated with the lifetime value record."}, {"name": "calculated_date", "data_type": "timestamp", "is_categorical": false, "is_datetime": true, "description": "The date on which the customer lifetime value was calculated."}, {"name": "predicted_value", "data_type": "decimal", "is_categorical": false, "is_datetime": false, "description": "The estimated total revenue expected from the customer over their lifetime."}, {"name": "confidence_interval", "data_type": "decimal", "is_categorical": false, "is_datetime": false, "description": "A statistical range that indicates the uncertainty around the predicted value, providing insight into the reliability of the estimate."}], "sample_rows": []}, {"table_id": "6f7fed02-14e7-462e-a734-9d62964bbef6", "table_name": "analytics.campaign_metrics", "schema_id": "analytics", "description": "This table contains the following fields: metric_id, campaign_id, metric_name, metric_value, metric_date. The purpose of the 'campaign_metrics' table is to store performance metrics related to various marketing campaigns, allowing for analysis and reporting on the effectiveness of each campaign over time.", "field_count": 5, "fields": [{"name": "metric_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each metric entry."}, {"name": "campaign_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The identifier for the campaign to which the metric belongs."}, {"name": "metric_name", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The name of the metric being recorded (e.g., impressions, clicks, conversions)."}, {"name": "metric_value", "data_type": "decimal", "is_categorical": false, "is_datetime": false, "description": "The numerical value of the metric recorded for the campaign."}, {"name": "metric_date", "data_type": "timestamp", "is_categorical": false, "is_datetime": true, "description": "The date when the metric was recorded, indicating the time period of the data."}], "sample_rows": []}, {"table_id": "9b98559f-faf1-4327-9043-96ab41a52912", "table_name": "analytics.sales_forecast", "schema_id": "analytics", "description": "This table contains the following fields: forecast_id, product_id, forecast_date, predicted_sales, confidence_interval. The 'sales_forecast' table is used to store and manage sales predictions for various products over specific dates, allowing businesses to plan inventory and marketing strategies based on expected demand.", "field_count": 5, "fields": [{"name": "forecast_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each sales forecast entry."}, {"name": "product_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The identifier for the product associated with the sales forecast."}, {"name": "forecast_date", "data_type": "timestamp", "is_categorical": false, "is_datetime": true, "description": "The date for which the sales forecast is made."}, {"name": "predicted_sales", "data_type": "decimal", "is_categorical": false, "is_datetime": false, "description": "The estimated number of units expected to be sold on the forecast date."}, {"name": "confidence_interval", "data_type": "decimal", "is_categorical": false, "is_datetime": false, "description": "A statistical range that indicates the level of certainty in the predicted sales figure."}], "sample_rows": []}, {"table_id": "6e4d1a0b-fb57-40ae-8bc2-129d73c56f67", "table_name": "analytics.search_queries", "schema_id": "analytics", "description": "This table contains the following fields: query_id, user_id, query_text, search_date, results_count. The 'search_queries' table is designed to store information about user search queries within the application. It tracks each query made by users, including who made the query, what was searched, when it occurred, and how many results were returned.", "field_count": 5, "fields": [{"name": "query_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each search query."}, {"name": "user_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The identifier for the user who made the search query."}, {"name": "query_text", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "The actual text of the search query entered by the user."}, {"name": "search_date", "data_type": "timestamp", "is_categorical": false, "is_datetime": true, "description": "The date and time when the search query was made."}, {"name": "results_count", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The number of results returned for the search query."}], "sample_rows": []}, {"table_id": "34ff920c-9469-48b0-ba5b-71ac05f63d27", "table_name": "analytics.segment_members", "schema_id": "analytics", "description": "This table contains the following fields: member_id, segment_id, customer_id, added_at. The 'segment_members' table is used to track the association between customers and specific segments within a marketing or analytics framework. It allows for the organization of customers into defined groups (segments) for targeted marketing efforts or analysis.", "field_count": 4, "fields": [{"name": "member_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each member in the segment."}, {"name": "segment_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for the segment to which the member belongs."}, {"name": "customer_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for the customer associated with the segment."}, {"name": "added_at", "data_type": "timestamp", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the member was added to the segment."}], "sample_rows": []}, {"table_id": "9bebebd2-bd45-4f94-9db0-6b6af62c8577", "table_name": "analytics.product_views", "schema_id": "analytics", "description": "This table contains the following fields: view_id, product_id, user_id, view_date, view_duration. The 'product_views' table is designed to track the interactions users have with products on an e-commerce platform. It records each instance a user views a product, capturing essential details about the view for analytics and reporting purposes.", "field_count": 5, "fields": [{"name": "view_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each product view record."}, {"name": "product_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The identifier of the product that was viewed."}, {"name": "user_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The identifier of the user who viewed the product."}, {"name": "view_date", "data_type": "timestamp", "is_categorical": false, "is_datetime": true, "description": "The date and time when the product was viewed."}, {"name": "view_duration", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The duration of time the user spent viewing the product, measured in seconds."}], "sample_rows": "{\n  \"view_id\": 123,\n  \"product_id\": 456,\n  \"user_id\": 789,\n  \"view_date\": \"2025-03-01T12:00:00\",\n  \"view_duration\": 1500\n}"}, {"table_id": "cee59b70-43c3-4667-b9a0-05cde0913b22", "table_name": "analytics.page_views", "schema_id": "analytics", "description": "This table contains the following fields: view_id, page_url, user_id, view_date, session_id. The 'page_views' table is used to track the number of views for each page on a website, capturing essential details about each view event, including which user viewed the page, when the view occurred, and the session during which the view took place.", "field_count": 5, "fields": [{"name": "view_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each page view record."}, {"name": "page_url", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "The URL of the page that was viewed."}, {"name": "user_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The identifier of the user who viewed the page."}, {"name": "view_date", "data_type": "timestamp", "is_categorical": false, "is_datetime": true, "description": "The date and time when the page view occurred."}, {"name": "session_id", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The identifier for the session during which the page view took place."}], "sample_rows": "{\n  \"view_id\": 2,\n  \"page_url\": \"http://www.example.com/\",\n  \"user_id\": 789,\n  \"view_date\": \"2025-06-15T14:22:10\",\n  \"session_id\": \"f1e2d3c4-b5a6-7b8c-9d0e-1f2g3h4i5j6k\"\n}"}, {"table_id": "37ae41ca-12b4-4452-bf4c-8bb39f37b829", "table_name": "analytics.user_activity", "schema_id": "analytics", "description": "This table contains the following fields: activity_id, user_id, activity_type, activity_date, details. The 'user_activity' table is designed to track and log various activities performed by users within the application. It captures essential information about each activity, allowing for analysis of user engagement and behavior over time.", "field_count": 5, "fields": [{"name": "activity_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each activity record."}, {"name": "user_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The identifier for the user who performed the activity."}, {"name": "activity_type", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": true, "is_datetime": false, "description": "The type of activity performed by the user, such as 'login', 'purchase', or 'comment'.", "categorical_values": ["login", "logout", "search", "view_product"]}, {"name": "activity_date", "data_type": "timestamp", "is_categorical": false, "is_datetime": true, "description": "The date and time when the activity occurred."}, {"name": "details", "data_type": "json", "is_categorical": false, "is_datetime": false, "description": "Additional information or context about the activity."}], "sample_rows": "{\n  \"activity_id\": 2,\n  \"user_id\": 987,\n  \"activity_type\": \"add_to_cart\",\n  \"activity_date\": \"2025-02-25T14:30:12\",\n  \"details\": \"{\\\"ip\\\": \\\"************\\\", \\\"browser\\\": \\\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.82 Safari/537.36\\\"}\"\n}"}, {"table_id": "bd79d207-a9a8-4026-9270-05863fd745cd", "table_name": "finance.messaging_connections", "schema_id": "finance", "description": "This table contains the following fields: id, user_id, type, config, db_connection_id. The 'messaging_connections' table is designed to store information about various messaging connections established by users, including their configurations and types, enabling efficient management and retrieval of messaging data.", "field_count": 5, "fields": [{"name": "id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each messaging connection record."}, {"name": "user_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The identifier of the user associated with this messaging connection."}, {"name": "type", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The type of messaging connection (e.g., email, SMS, chat)."}, {"name": "config", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "A JSON object containing configuration settings specific to the messaging connection."}, {"name": "db_connection_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The identifier for the database connection used for this messaging service."}], "sample_rows": []}, {"table_id": "8971cc5c-382d-4303-bd15-3551029bfb27", "table_name": "finance.settings", "schema_id": "finance", "description": "This table contains the following fields: id, user_id, key, value. The 'settings' table is used to store configuration settings for users in the application. Each setting is associated with a specific user and consists of a key-value pair that defines the user's preferences or application settings.", "field_count": 4, "fields": [{"name": "id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each setting record."}, {"name": "user_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The identifier of the user to whom the setting belongs."}, {"name": "key", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The name of the setting, representing the specific configuration option."}, {"name": "value", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "The value associated with the key, defining the user's preference for that setting."}], "sample_rows": []}, {"table_id": "9f152898-74fb-4756-a2d0-a646d835c43e", "table_name": "finance.otps", "schema_id": "finance", "description": "This table contains the following fields: id, email, otp_code, expires_at, is_used, created_at. The 'otps' table is designed to store one-time password (OTP) information for user authentication purposes. It tracks the OTPs generated for users, their expiration times, and whether they have been used, facilitating secure login processes.", "field_count": 6, "fields": [{"name": "id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each OTP record."}, {"name": "email", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The email address of the user associated with the OTP."}, {"name": "otp_code", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The one-time password generated for user authentication."}, {"name": "expires_at", "data_type": "datetime", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the OTP will expire."}, {"name": "is_used", "data_type": "tinyint", "is_categorical": true, "is_datetime": false, "description": "A boolean flag indicating whether the OTP has been used.", "categorical_values": ["1"]}, {"name": "created_at", "data_type": "datetime", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the OTP record was created."}], "sample_rows": "{\n  \"id\": 2,\n  \"email\": \"<EMAIL>\",\n  \"otp_code\": \"543216\",\n  \"expires_at\": \"2026-07-15T14:45:00\",\n  \"is_used\": 0,\n  \"created_at\": \"2026-07-15T14:35:00\"\n}"}, {"table_id": "e85b5a61-9147-4ec7-97a0-6f263009610a", "table_name": "finance.pending_registrations", "schema_id": "finance", "description": "This table contains the following fields: id, email, full_name, hashed_password, organization_name, organization_description, created_at, is_verified. The purpose of the 'pending_registrations' table is to store information about users who have initiated the registration process but have not yet completed it. This includes their personal details, organizational affiliation, and the status of their verification process.", "field_count": 8, "fields": [{"name": "id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each pending registration entry."}, {"name": "email", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The email address of the user registering."}, {"name": "full_name", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The full name of the user registering."}, {"name": "hashed_password", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The hashed version of the user's password for security purposes."}, {"name": "organization_name", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The name of the organization the user is associated with."}, {"name": "organization_description", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "A brief description of the organization."}, {"name": "created_at", "data_type": "datetime", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the registration entry was created."}, {"name": "is_verified", "data_type": "tinyint", "is_categorical": true, "is_datetime": false, "description": "A boolean flag indicating whether the user's registration has been verified.", "categorical_values": ["1"]}], "sample_rows": "{\n  \"id\": 2,\n  \"email\": \"<EMAIL>\",\n  \"full_name\": \"john doe\",\n  \"hashed_password\": \"$2b$12$randomhashedpassword********90abcdefg\",\n  \"organization_name\": \"exampleorg\",\n  \"organization_description\": null,\n  \"created_at\": \"2025-07-01T12:00:00\",\n  \"is_verified\": 0\n}"}, {"table_id": "c0911d89-24eb-4209-a369-8d83a5e3dd6a", "table_name": "finance.users", "schema_id": "finance", "description": "This table contains the following fields: id, full_name, organization_name, email, hashed_password, organization_description, role. The 'users' table is designed to store information about individuals who have registered within the system, allowing for user management and authentication. It includes personal details, organizational affiliations, and security credentials necessary for user access and role assignment.", "field_count": 7, "fields": [{"name": "id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each user, typically an auto-incrementing integer."}, {"name": "full_name", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The complete name of the user, including first and last names."}, {"name": "organization_name", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The name of the organization that the user is affiliated with."}, {"name": "email", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The email address of the user, used for communication and login purposes."}, {"name": "hashed_password", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The user's password stored in a hashed format for security."}, {"name": "organization_description", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "A brief description of the organization that the user represents."}, {"name": "role", "data_type": "enum", "is_categorical": true, "is_datetime": false, "description": "The role assigned to the user within the system, indicating their level of access and permissions.", "categorical_values": ["member"]}], "sample_rows": "{\n  \"id\": 2,\n  \"full_name\": \"james smith\",\n  \"organization_name\": \"examplecorp\",\n  \"email\": \"<EMAIL>\",\n  \"hashed_password\": \"$2b$12$randomhashedpassword********90abcdefg\",\n  \"organization_description\": null,\n  \"role\": \"admin\"\n}"}, {"table_id": "b1b99f89-d643-4715-9bab-8c364cfba058", "table_name": "finance.connection_tables", "schema_id": "finance", "description": "This table contains the following fields: id, connection_id, schema_name, name, description, status, sample_row. The 'connection_tables' table is designed to store metadata about various database tables associated with specific connections. It helps in managing and organizing the schema information of tables that are linked to different data sources or connections.", "field_count": 7, "fields": [{"name": "id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each record in the table."}, {"name": "connection_id", "data_type": "int", "is_categorical": true, "is_datetime": false, "description": "The identifier for the connection associated with the table.", "categorical_values": ["7", "8"]}, {"name": "schema_name", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": true, "is_datetime": false, "description": "The name of the schema that the table belongs to.", "categorical_values": ["TRANSFORMED"]}, {"name": "name", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The name of the table."}, {"name": "description", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "A brief description of the table's purpose or content."}, {"name": "status", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": true, "is_datetime": false, "description": "The current status of the table (e.g., active, inactive).", "categorical_values": ["active", "inactive"]}, {"name": "sample_row", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "A sample row of data from the table, used for reference."}], "sample_rows": "{\n  \"id\": 256,\n  \"connection_id\": 12,\n  \"schema_name\": \"ANONYMIZED\",\n  \"name\": \"ANONYMIZED.AUTO_DISCOUNT_RULES\",\n  \"description\": \"This table contains the following fields: created_at, code, from_asset, to_asset, company_list, percent_higher_than_best, created_by, updated_by, updated_at. The purpose of this table is to define discount rules for rate tiers in an automated system, specifying the conditions under which discounts apply based on asset types and company criteria.\",\n  \"status\": \"inactive\",\n  \"sample_row\": \"[]\"\n}"}, {"table_id": "554a2e35-6031-4564-9cda-a6e48b1f52fa", "table_name": "finance.connections", "schema_id": "finance", "description": "This table contains the following fields: id, user_id, name, database_type, database_platform, connection_params, created_at, updated_at, status. The 'connections' table is designed to store information about various database connections established by users. It maintains details about each connection, including the user who created it, the type and platform of the database, connection parameters, and timestamps for creation and updates, as well as the current status of the connection.", "field_count": 9, "fields": [{"name": "id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each connection record."}, {"name": "user_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The identifier of the user who owns the connection."}, {"name": "name", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "A user-defined name for the connection."}, {"name": "database_type", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": true, "is_datetime": false, "description": "The type of database (e.g., SQL, NoSQL) that the connection is associated with.", "categorical_values": ["sql"]}, {"name": "database_platform", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": true, "is_datetime": false, "description": "The specific database platform (e.g., MySQL, PostgreSQL) used for the connection.", "categorical_values": ["snowflake"]}, {"name": "connection_params", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "A JSON object containing the parameters required to establish the connection."}, {"name": "created_at", "data_type": "datetime", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the connection record was created."}, {"name": "updated_at", "data_type": "datetime", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the connection record was last updated."}, {"name": "status", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": true, "is_datetime": false, "description": "The current status of the connection (e.g., active, inactive).", "categorical_values": ["connected"]}], "sample_rows": "{\n  \"id\": 42,\n  \"user_id\": 23,\n  \"name\": \"random_name_example\",\n  \"database_type\": \"nosql\",\n  \"database_platform\": \"mongodb\",\n  \"connection_params\": \"{\\\"user\\\": \\\"RANDOMUSER\\\", \\\"password\\\": \\\"SecurePass9\\\", \\\"account\\\": \\\"abcxyz-********\\\", \\\"database\\\": \\\"DUMMYDB\\\", \\\"warehouse\\\": \\\"DATAWAREHOUSE\\\", \\\"role\\\": \\\"DATAANALYST\\\"}\",\n  \"created_at\": \"2025-08-15T12:30:45\",\n  \"updated_at\": \"2025-08-15T12:30:45\",\n  \"status\": \"disconnected\"\n}"}, {"table_id": "f4510ae2-d030-452d-b2fa-a98a55832194", "table_name": "finance.fields", "schema_id": "finance", "description": "This table contains the following fields: id, connection_table_id, name, data_type, description, is_categorical, is_datetime, categorical_values, status. The 'fields' table is designed to store metadata about various fields in a database, including their types, descriptions, and characteristics, which helps in understanding the structure and constraints of the data being managed.", "field_count": 9, "fields": [{"name": "id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each field in the table."}, {"name": "connection_table_id", "data_type": "int", "is_categorical": false, "is_datetime": false, "description": "The identifier of the table to which this field belongs, establishing a relationship between the field and its parent table."}, {"name": "name", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": false, "is_datetime": false, "description": "The name of the field, which is used to reference it in queries and operations."}, {"name": "data_type", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": true, "is_datetime": false, "description": "The type of data that the field can hold, such as integer, string, or boolean.", "categorical_values": ["ARRAY", "BOOLEAN", "DATE", "NUMBER", "TEXT", "TIMESTAMP_NTZ", "VARIANT"]}, {"name": "description", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "A textual description providing additional context or information about the field."}, {"name": "is_categorical", "data_type": "tinyint", "is_categorical": true, "is_datetime": false, "description": "A boolean flag indicating whether the field is categorical (true) or not (false).", "categorical_values": ["0", "1"]}, {"name": "is_datetime", "data_type": "tinyint", "is_categorical": true, "is_datetime": false, "description": "A boolean flag indicating whether the field is of datetime type (true) or not (false).", "categorical_values": ["0", "1"]}, {"name": "categorical_values", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "A list of possible values for the field if it is categorical, helping to define the valid entries."}, {"name": "status", "data_type": "<PERSON><PERSON><PERSON>", "is_categorical": true, "is_datetime": false, "description": "The current status of the field, which may indicate whether it is active, deprecated, or removed.", "categorical_values": ["active"]}], "sample_rows": "{\n  \"id\": 4823,\n  \"connection_table_id\": 256,\n  \"name\": \"created_at\",\n  \"data_type\": \"TIMESTAMP_NTZ\",\n  \"description\": \"The timestamp indicating when the discount rule was created.\",\n  \"is_categorical\": 0,\n  \"is_datetime\": 1,\n  \"categorical_values\": null,\n  \"status\": \"inactive\"\n}"}]}